{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Loading required package: Matrix\n", "\n", "Warning message:\n", "“package ‘Matrix’ was built under R version 4.3.3”\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘readr’ was built under R version 4.3.3”\n", "Warning message:\n", "“package ‘ggplot2’ was built under R version 4.3.3”\n", "\n", "Please cite as: \n", "\n", "\n", " <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2022). stargazer: Well-Formatted Regression and Summary Statistics Tables.\n", "\n", " R package version 5.2.3. https://CRAN.R-project.org/package=stargazer \n", "\n", "\n", "Loading required package: zoo\n", "\n", "\n", "Attaching package: ‘zoo’\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    as.Date, as.Date.numeric\n", "\n", "\n", "\n", "Attaching package: ‘lmerTest’\n", "\n", "\n", "The following object is masked from ‘package:lme4’:\n", "\n", "    lmer\n", "\n", "\n", "The following object is masked from ‘package:stats’:\n", "\n", "    step\n", "\n", "\n", "Registered S3 method overwritten by 'broom':\n", "  method        from \n", "  nobs.multinom MuMIn\n", "\n", "\n", "Attaching package: ‘survminer’\n", "\n", "\n", "The following object is masked from ‘package:survival’:\n", "\n", "    myeloma\n", "\n", "\n", "Loading required package: carData\n", "\n", "Loading required package: bdsmatrix\n", "\n", "\n", "Attaching package: ‘bdsmatrix’\n", "\n", "\n", "The following object is masked from ‘package:base’:\n", "\n", "    backsolve\n", "\n", "\n", "Registered S3 methods overwritten by 'coxme':\n", "  method        from \n", "  formula.coxme MuMIn\n", "  logLik.lmekin Mu<PERSON>n\n", "\n"]}], "source": ["# Load required libraries\n", "library(stats)\n", "library(lme4)\n", "library(readr)\n", "library(ggplot2)\n", "library(stargazer)\n", "library(multiwayvcov)\n", "library(lmtest)\n", "library(MuMIn)\n", "library(lmerTest)\n", "library(survival)\n", "library(ggpubr)\n", "library(survminer)\n", "library(car)\n", "library(coxme)\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/did_result_20250227/compiled_data_test_with_features_and_growth_phase_and_newcomers_new.csv\")\n", "# compiled_data_test <- read.csv(\"../result/did_result_20250212/compiled_data_test_with_features.csv\")\n", "# compiled_data_test <- read.csv(\"../result/standardized_productivity_20250202/compiled_data_test_with_features.csv\")\n", "\n", "\n", "\n", "# # Model 1: Fixed Effects Only\n", "# model_fixed_effects_only <- lmer(\n", "#   log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "#     log_project_commits + log_project_contributors + log_project_age + \n", "#     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "#   REML = FALSE,\n", "#   data = compiled_data_test\n", "# )\n", "\n", "# # Calculate VIF\n", "# vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "# print(vif_model_fixed_effects_only)\n", "\n", "# # Summary of the model\n", "# summary(model_fixed_effects_only)\n", "\n", "# # Calculate R-squared values\n", "# r.squaredGLMM(model_fixed_effects_only)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"ename": "ERROR", "evalue": "Error in h(simpleError(msg, call)): error in evaluating the argument 'x' in selecting a method for function 'print': could not find function \"count\"\n", "output_type": "error", "traceback": ["Error in h(simpleError(msg, call)): error in evaluating the argument 'x' in selecting a method for function 'print': could not find function \"count\"\nTraceback:\n", "1. print(., n = Inf)", "2. .handleSimpleError(function (cond) \n . .Internal(C_tryCatchHelper(addr, 1L, cond)), \"could not find function \\\"count\\\"\", \n .     base::quote(count(., project_main_language)))", "3. h(simpleError(msg, call))", "4. .handleSimpleError(function (cnd) \n . {\n .     watcher$capture_plot_and_output()\n .     cnd <- sanitize_call(cnd)\n .     watcher$push(cnd)\n .     switch(on_error, continue = invokeRestart(\"eval_continue\"), \n .         stop = invokeRestart(\"eval_stop\"), error = NULL)\n . }, \"error in evaluating the argument 'x' in selecting a method for function 'print': could not find function \\\"count\\\"\", \n .     base::quote(h(simpleError(msg, call))))"]}], "source": ["# 检查过滤后数据中是否存在所有语言类别\n", "compiled_data_test_exclude_na_growth_phase %>%\n", "  count(project_main_language) %>%\n", "  print(n = Inf)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model failed to converge with max|grad| = 0.0184783 (tol = 0.002, component 1)”\n", "Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                              is_post_treatment \n", "                                       1.382491 \n", "                                     is_treated \n", "                                       1.059619 \n", "                            log_project_commits \n", "                                       1.969374 \n", "                       log_project_contributors \n", "                                       2.237557 \n", "                                log_project_age \n", "                                       1.443337 \n", "                   is_post_treatment:is_treated \n", "                                      24.421885 \n", "        is_post_treatment:is_treated:log_tenure \n", "                                      26.763063 \n", "is_post_treatment:is_treated:log_commit_percent \n", "                                       2.057081 \n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +  \n", "    is_treated:is_post_treatment:log_tenure + is_treated:is_post_treatment:log_commit_percent +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6309603  6309760 -3154790  6309579  3503009 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8223 -0.4991 -0.1666  0.5284  8.7372 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32858  0.5732  \n", " time_cohort_effect (Intercept) 0.02554  0.1598  \n", " Residual                       0.30282  0.5503  \n", "Number of obs: 3503021, groups:  \n", "repo_cohort_effect, 140744; time_cohort_effect, 140734\n", "\n", "Fixed effects:\n", "                                                  Estimate Std. Error\n", "(Intercept)                                      2.806e-01  1.123e-02\n", "is_post_treatment                               -1.957e-02  1.219e-03\n", "is_treated                                      -6.727e-02  3.204e-03\n", "log_project_commits                              1.767e-01  1.380e-03\n", "log_project_contributors                         2.938e-01  1.997e-03\n", "log_project_age                                 -2.402e-01  1.806e-03\n", "is_post_treatment:is_treated                    -2.119e-01  5.009e-03\n", "is_post_treatment:is_treated:log_tenure          2.386e-02  8.005e-04\n", "is_post_treatment:is_treated:log_commit_percent -2.554e-01  6.273e-03\n", "                                                        df t value Pr(>|t|)    \n", "(Intercept)                                      2.601e+05   24.99   <2e-16 ***\n", "is_post_treatment                                1.170e+05  -16.05   <2e-16 ***\n", "is_treated                                       1.266e+05  -21.00   <2e-16 ***\n", "log_project_commits                              1.779e+05  128.05   <2e-16 ***\n", "log_project_contributors                         1.907e+05  147.09   <2e-16 ***\n", "log_project_age                                  2.688e+05 -132.96   <2e-16 ***\n", "is_post_treatment:is_treated                     1.048e+06  -42.30   <2e-16 ***\n", "is_post_treatment:is_treated:log_tenure          9.610e+05   29.80   <2e-16 ***\n", "is_post_treatment:is_treated:log_commit_percent  9.364e+05  -40.72   <2e-16 ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "\n", "Correlation of Fixed Effects:\n", "            (Intr) is_ps_ is_trt lg_prjct_cm lg_prjct_cn lg_prjct_g is__:_\n", "is_pst_trtm  0.130                                                        \n", "is_treated  -0.090  0.106                                                 \n", "lg_prjct_cm -0.316  0.003  0.084                                          \n", "lg_prjct_cn  0.282 -0.020 -0.129 -0.611                                   \n", "log_prjct_g -0.808 -0.160 -0.041 -0.112      -0.332                       \n", "is_pst_tr:_  0.015 -0.110 -0.038  0.015      -0.013      -0.013           \n", "is_pst_:_:_ -0.017 -0.005 -0.003 -0.018       0.007       0.025     -0.955\n", "is_ps_:_:__  0.002 -0.002 -0.004  0.023       0.034      -0.036      0.174\n", "            is__:_:_\n", "is_pst_trtm         \n", "is_treated          \n", "lg_prjct_cm         \n", "lg_prjct_cn         \n", "log_prjct_g         \n", "is_pst_tr:_         \n", "is_pst_:_:_         \n", "is_ps_:_:__ -0.358  \n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model failed to converge with max|grad| = 0.0184783 (tol = 0.002, component 1)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.2580681</td><td>0.6580049</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.2580681 & 0.6580049\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.2580681 | 0.6580049 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.2580681 0.6580049"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 2: Fixed Effects + Developer Characteristics\n", "model_fixed_effects_developer <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "                     is_treated:is_post_treatment:log_tenure +\n", "                     is_treated:is_post_treatment:log_commit_percent +\n", "                    #  is_treated:is_post_treatment:log_commits +\n", "                     log_project_commits + log_project_contributors + log_project_age + \n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_developer <- vif(model_fixed_effects_developer)\n", "print(vif_model_fixed_effects_developer)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_developer)\n", "\n", "# Calculate R-squared values\n", "<PERSON>.<PERSON>(model_fixed_effects_developer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model failed to converge with max|grad| = 0.00386074 (tol = 0.002, component 1)”\n", "Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                                                             GVIF\n", "is_post_treatment                                                        1.385385\n", "is_treated                                                               1.060417\n", "log_project_commits                                                      1.999123\n", "log_project_contributors                                                 2.285563\n", "log_project_age                                                          1.453669\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       46.651165\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  32.427082\n", "is_post_treatment:is_treated:log_project_age_before_treatment          115.522547\n", "is_post_treatment:is_treated:growth_phase                              123.306262\n", "is_post_treatment:is_treated:log_newcomers                               2.978466\n", "is_post_treatment:is_treated:project_main_language                      18.426758\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.177024\n", "is_treated                                                                    1.029766\n", "log_project_commits                                                           1.413904\n", "log_project_contributors                                                      1.511808\n", "log_project_age                                                               1.205682\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             6.830166\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        5.694478\n", "is_post_treatment:is_treated:log_project_age_before_treatment                10.748142\n", "is_post_treatment:is_treated:growth_phase                                     1.618447\n", "is_post_treatment:is_treated:log_newcomers                                    1.725823\n", "is_post_treatment:is_treated:project_main_language                            1.175717\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 24 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_project_commits_before_treatment +  \n", "    is_treated:is_post_treatment:log_project_contributors_before_treatment +  \n", "    is_treated:is_post_treatment:log_project_age_before_treatment +  \n", "    is_treated:is_post_treatment:growth_phase + is_treated:is_post_treatment:log_newcomers +  \n", "    is_treated:is_post_treatment:project_main_language + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6260801  6261154 -3130374  6260747  3482206 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8303 -0.5017 -0.1670  0.5267  8.7495 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32173  0.5672  \n", " time_cohort_effect (Intercept) 0.02514  0.1585  \n", " Residual                       0.30210  0.5496  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             2.661e-01\n", "is_post_treatment                                                      -1.881e-02\n", "is_treated                                                             -6.390e-02\n", "log_project_commits                                                     1.787e-01\n", "log_project_contributors                                                2.792e-01\n", "log_project_age                                                        -2.332e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.611e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.765e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.293e-02\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -3.873e-01\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -2.860e-01\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -9.433e-01\n", "is_post_treatment:is_treated:growth_phasesaturation                    -3.065e-01\n", "is_post_treatment:is_treated:growth_phasesteady                        -3.016e-01\n", "is_post_treatment:is_treated:log_newcomers                              9.296e-02\n", "is_post_treatment:is_treated:project_main_languageC#                   -1.124e-02\n", "is_post_treatment:is_treated:project_main_languageC++                  -3.176e-03\n", "is_post_treatment:is_treated:project_main_languageGo                   -7.103e-03\n", "is_post_treatment:is_treated:project_main_languageJava                 -7.269e-03\n", "is_post_treatment:is_treated:project_main_languageJavaScript           -2.037e-02\n", "is_post_treatment:is_treated:project_main_languagePHP                  -2.073e-02\n", "is_post_treatment:is_treated:project_main_languagePython               -1.911e-02\n", "is_post_treatment:is_treated:project_main_languageRust                 -3.934e-02\n", "is_post_treatment:is_treated:project_main_languageTypeScript           -8.566e-03\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             1.120e-02\n", "is_post_treatment                                                       1.217e-03\n", "is_treated                                                              3.182e-03\n", "log_project_commits                                                     1.382e-03\n", "log_project_contributors                                                2.007e-03\n", "log_project_age                                                         1.804e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.051e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.612e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.559e-03\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   1.077e-02\n", "is_post_treatment:is_treated:growth_phasedecelerating                   1.088e-02\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 5.262e-02\n", "is_post_treatment:is_treated:growth_phasesaturation                     1.155e-02\n", "is_post_treatment:is_treated:growth_phasesteady                         1.083e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.018e-03\n", "is_post_treatment:is_treated:project_main_languageC#                    6.177e-03\n", "is_post_treatment:is_treated:project_main_languageC++                   5.383e-03\n", "is_post_treatment:is_treated:project_main_languageGo                    5.285e-03\n", "is_post_treatment:is_treated:project_main_languageJava                  5.227e-03\n", "is_post_treatment:is_treated:project_main_languageJavaScript            4.755e-03\n", "is_post_treatment:is_treated:project_main_languagePHP                   5.899e-03\n", "is_post_treatment:is_treated:project_main_languagePython                4.635e-03\n", "is_post_treatment:is_treated:project_main_languageRust                  6.599e-03\n", "is_post_treatment:is_treated:project_main_languageTypeScript            5.179e-03\n", "                                                                               df\n", "(Intercept)                                                             2.487e+05\n", "is_post_treatment                                                       1.152e+05\n", "is_treated                                                              1.255e+05\n", "log_project_commits                                                     1.836e+05\n", "log_project_contributors                                                1.950e+05\n", "log_project_age                                                         2.561e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       8.976e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  9.047e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           9.554e+05\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   9.756e+05\n", "is_post_treatment:is_treated:growth_phasedecelerating                   9.752e+05\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 9.498e+05\n", "is_post_treatment:is_treated:growth_phasesaturation                     9.683e+05\n", "is_post_treatment:is_treated:growth_phasesteady                         9.732e+05\n", "is_post_treatment:is_treated:log_newcomers                              9.429e+05\n", "is_post_treatment:is_treated:project_main_languageC#                    9.267e+05\n", "is_post_treatment:is_treated:project_main_languageC++                   9.265e+05\n", "is_post_treatment:is_treated:project_main_languageGo                    9.269e+05\n", "is_post_treatment:is_treated:project_main_languageJava                  9.266e+05\n", "is_post_treatment:is_treated:project_main_languageJavaScript            9.241e+05\n", "is_post_treatment:is_treated:project_main_languagePHP                   9.227e+05\n", "is_post_treatment:is_treated:project_main_languagePython                9.245e+05\n", "is_post_treatment:is_treated:project_main_languageRust                  9.253e+05\n", "is_post_treatment:is_treated:project_main_languageTypeScript            9.252e+05\n", "                                                                        t value\n", "(Intercept)                                                              23.758\n", "is_post_treatment                                                       -15.461\n", "is_treated                                                              -20.083\n", "log_project_commits                                                     129.243\n", "log_project_contributors                                                139.071\n", "log_project_age                                                        -129.297\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         1.532\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -23.356\n", "is_post_treatment:is_treated:log_project_age_before_treatment            21.119\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   -35.973\n", "is_post_treatment:is_treated:growth_phasedecelerating                   -26.277\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 -17.928\n", "is_post_treatment:is_treated:growth_phasesaturation                     -26.529\n", "is_post_treatment:is_treated:growth_phasesteady                         -27.842\n", "is_post_treatment:is_treated:log_newcomers                               91.280\n", "is_post_treatment:is_treated:project_main_languageC#                     -1.820\n", "is_post_treatment:is_treated:project_main_languageC++                    -0.590\n", "is_post_treatment:is_treated:project_main_languageGo                     -1.344\n", "is_post_treatment:is_treated:project_main_languageJava                   -1.391\n", "is_post_treatment:is_treated:project_main_languageJavaScript             -4.284\n", "is_post_treatment:is_treated:project_main_languagePHP                    -3.515\n", "is_post_treatment:is_treated:project_main_languagePython                 -4.122\n", "is_post_treatment:is_treated:project_main_languageRust                   -5.962\n", "is_post_treatment:is_treated:project_main_languageTypeScript             -1.654\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       0.12550\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasedecelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 < 2e-16\n", "is_post_treatment:is_treated:growth_phasesaturation                     < 2e-16\n", "is_post_treatment:is_treated:growth_phasesteady                         < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:project_main_languageC#                    0.06880\n", "is_post_treatment:is_treated:project_main_languageC++                   0.55521\n", "is_post_treatment:is_treated:project_main_languageGo                    0.17895\n", "is_post_treatment:is_treated:project_main_languageJava                  0.16435\n", "is_post_treatment:is_treated:project_main_languageJavaScript           1.83e-05\n", "is_post_treatment:is_treated:project_main_languagePHP                   0.00044\n", "is_post_treatment:is_treated:project_main_languagePython               3.75e-05\n", "is_post_treatment:is_treated:project_main_languageRust                 2.50e-09\n", "is_post_treatment:is_treated:project_main_languageTypeScript            0.09816\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  ***\n", "is_post_treatment:is_treated:growth_phasedecelerating                  ***\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                ***\n", "is_post_treatment:is_treated:growth_phasesaturation                    ***\n", "is_post_treatment:is_treated:growth_phasesteady                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:project_main_languageC#                   .  \n", "is_post_treatment:is_treated:project_main_languageC++                     \n", "is_post_treatment:is_treated:project_main_languageGo                      \n", "is_post_treatment:is_treated:project_main_languageJava                    \n", "is_post_treatment:is_treated:project_main_languageJavaScript           ***\n", "is_post_treatment:is_treated:project_main_languagePHP                  ***\n", "is_post_treatment:is_treated:project_main_languagePython               ***\n", "is_post_treatment:is_treated:project_main_languageRust                 ***\n", "is_post_treatment:is_treated:project_main_languageTypeScript           .  \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model failed to converge with max|grad| = 0.00386074 (tol = 0.002, component 1)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.2561632</td><td>0.6537335</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.2561632 & 0.6537335\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.2561632 | 0.6537335 |\n", "\n"], "text/plain": ["     R2m       R2c      \n", "[1,] 0.2561632 0.6537335"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 3: Fixed Effects + Project Characteristics\n", "model_fixed_effects_project <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_project_commits_before_treatment + is_treated:is_post_treatment:log_project_contributors_before_treatment +\n", "                     is_treated:is_post_treatment:log_project_age_before_treatment + \n", "                                          is_treated:is_post_treatment:growth_phase +\n", "                      is_treated:is_post_treatment:log_newcomers +\n", "                     is_treated:is_post_treatment:project_main_language +\n", "                                          log_project_commits + log_project_contributors + log_project_age + \n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_project <- vif(model_fixed_effects_project)\n", "print(vif_model_fixed_effects_project)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_project)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_project)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                             GVIF Df GVIF^(1/(2*Df))\n", "is_post_treatment        1.040405  1        1.020002\n", "is_treated               1.027128  1        1.013473\n", "log_project_commits      2.022336  1        1.422088\n", "log_project_contributors 2.368087  1        1.538859\n", "log_project_age          1.543973  1        1.242567\n", "growth_phase             1.064162  5        1.006238\n", "log_newcomers            1.208856  1        1.099480\n", "project_main_language    1.038760  9        1.002115\n"]}], "source": ["model_stage1 <- lmer(\n", "  log_pr_throughput ~ \n", "    is_post_treatment + \n", "    is_treated +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    growth_phase +\n", "    log_newcomers +\n", "    project_main_language +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# 检查VIF\n", "vif_stage1 <- car::vif(model_stage1)\n", "print(vif_stage1)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 21 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + log_project_commits +  \n", "    log_project_contributors + log_project_age + growth_phase +  \n", "    log_newcomers + project_main_language + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6255494  6255807 -3127723  6255446  3482209 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.7534 -0.4971 -0.1609  0.5287  8.8014 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.26713  0.5168  \n", " time_cohort_effect (Intercept) 0.02612  0.1616  \n", " Residual                       0.30364  0.5510  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                  Estimate Std. Error         df t value\n", "(Intercept)                     -3.015e-01  2.766e-01  1.393e+05  -1.090\n", "is_post_treatment               -7.016e-02  1.069e-03  6.336e+04 -65.620\n", "is_treated                      -1.039e-01  2.864e-03  1.121e+05 -36.281\n", "log_project_commits              1.604e-01  1.294e-03  1.645e+05 123.993\n", "log_project_contributors         2.248e-01  1.902e-03  1.772e+05 118.174\n", "log_project_age                 -1.712e-01  1.756e-03  2.457e+05 -97.502\n", "growth_phaseaccelerating         3.430e-01  2.764e-01  1.391e+05   1.241\n", "growth_phasedecelerating         1.238e-01  2.764e-01  1.391e+05   0.448\n", "growth_phasefirst 3 months       6.036e-01  2.868e-01  1.391e+05   2.105\n", "growth_phasesaturation          -2.185e-02  2.764e-01  1.391e+05  -0.079\n", "growth_phasesteady               1.582e-01  2.764e-01  1.391e+05   0.572\n", "log_newcomers                    1.577e-01  1.354e-03  1.435e+05 116.456\n", "project_main_languageC#          1.483e-01  9.111e-03  1.392e+05  16.278\n", "project_main_languageC++         1.129e-01  7.937e-03  1.392e+05  14.222\n", "project_main_languageGo          2.642e-01  7.738e-03  1.393e+05  34.143\n", "project_main_languageJava        1.390e-01  7.713e-03  1.392e+05  18.026\n", "project_main_languageJavaScript  5.311e-02  6.952e-03  1.394e+05   7.640\n", "project_main_languagePHP         2.446e-02  8.683e-03  1.393e+05   2.817\n", "project_main_languagePython      7.312e-02  6.822e-03  1.392e+05  10.718\n", "project_main_languageRust        1.454e-01  9.698e-03  1.392e+05  14.988\n", "project_main_languageTypeScript  2.807e-01  7.608e-03  1.393e+05  36.901\n", "                                Pr(>|t|)    \n", "(Intercept)                      0.27577    \n", "is_post_treatment                < 2e-16 ***\n", "is_treated                       < 2e-16 ***\n", "log_project_commits              < 2e-16 ***\n", "log_project_contributors         < 2e-16 ***\n", "log_project_age                  < 2e-16 ***\n", "growth_phaseaccelerating         0.21459    \n", "growth_phasedecelerating         0.65413    \n", "growth_phasefirst 3 months       0.03531 *  \n", "growth_phasesaturation           0.93698    \n", "growth_phasesteady               0.56708    \n", "log_newcomers                    < 2e-16 ***\n", "project_main_languageC#          < 2e-16 ***\n", "project_main_languageC++         < 2e-16 ***\n", "project_main_languageGo          < 2e-16 ***\n", "project_main_languageJava        < 2e-16 ***\n", "project_main_languageJavaScript 2.19e-14 ***\n", "project_main_languagePHP         0.00485 ** \n", "project_main_languagePython      < 2e-16 ***\n", "project_main_languageRust        < 2e-16 ***\n", "project_main_languageTypeScript  < 2e-16 ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["summary(model_stage1)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model failed to converge with max|grad| = 0.0212705 (tol = 0.002, component 1)”\n", "Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                                                             GVIF\n", "log_project_commits                                                      3.237807\n", "log_project_contributors                                                 3.626694\n", "log_project_age                                                          2.301406\n", "is_post_treatment:log_project_commits_before_treatment                  59.007321\n", "is_treated:log_project_commits_before_treatment                         41.895185\n", "is_post_treatment:log_project_contributors_before_treatment             41.227909\n", "is_treated:log_project_contributors_before_treatment                    28.899298\n", "is_post_treatment:log_project_age_before_treatment                     147.246125\n", "is_treated:log_project_age_before_treatment                             97.287476\n", "is_post_treatment:growth_phase                                         561.362373\n", "is_treated:growth_phase                                                125.727868\n", "is_post_treatment:log_newcomers                                          3.920316\n", "is_treated:log_newcomers                                                 2.417843\n", "is_post_treatment:project_main_language                                307.089725\n", "is_treated:project_main_language                                        18.652381\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       63.646180\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  44.423218\n", "is_post_treatment:is_treated:log_project_age_before_treatment          164.086499\n", "is_post_treatment:is_treated:growth_phase                              633.045896\n", "is_post_treatment:is_treated:log_newcomers                               4.113325\n", "is_post_treatment:is_treated:project_main_language                     342.222263\n", "                                                                       Df\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:log_project_commits_before_treatment                  1\n", "is_treated:log_project_commits_before_treatment                         1\n", "is_post_treatment:log_project_contributors_before_treatment             1\n", "is_treated:log_project_contributors_before_treatment                    1\n", "is_post_treatment:log_project_age_before_treatment                      1\n", "is_treated:log_project_age_before_treatment                             1\n", "is_post_treatment:growth_phase                                          5\n", "is_treated:growth_phase                                                 5\n", "is_post_treatment:log_newcomers                                         1\n", "is_treated:log_newcomers                                                1\n", "is_post_treatment:project_main_language                                 9\n", "is_treated:project_main_language                                        9\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "log_project_commits                                                           1.799391\n", "log_project_contributors                                                      1.904388\n", "log_project_age                                                               1.517039\n", "is_post_treatment:log_project_commits_before_treatment                        7.681622\n", "is_treated:log_project_commits_before_treatment                               6.472649\n", "is_post_treatment:log_project_contributors_before_treatment                   6.420896\n", "is_treated:log_project_contributors_before_treatment                          5.375807\n", "is_post_treatment:log_project_age_before_treatment                           12.134501\n", "is_treated:log_project_age_before_treatment                                   9.863441\n", "is_post_treatment:growth_phase                                                1.883321\n", "is_treated:growth_phase                                                       1.621598\n", "is_post_treatment:log_newcomers                                               1.979979\n", "is_treated:log_newcomers                                                      1.554941\n", "is_post_treatment:project_main_language                                       1.374616\n", "is_treated:project_main_language                                              1.176512\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             7.977856\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        6.665074\n", "is_post_treatment:is_treated:log_project_age_before_treatment                12.809625\n", "is_post_treatment:is_treated:growth_phase                                     1.906091\n", "is_post_treatment:is_treated:log_newcomers                                    2.028133\n", "is_post_treatment:is_treated:project_main_language                            1.382913\n"]}], "source": ["model_optimized <- lmer(\n", "  log_pr_throughput ~ \n", "    (is_post_treatment * is_treated) : log_project_commits_before_treatment +\n", "    (is_post_treatment * is_treated) : log_project_contributors_before_treatment +\n", "    (is_post_treatment * is_treated) : log_project_age_before_treatment +\n", "    (is_post_treatment * is_treated) : growth_phase +\n", "    (is_post_treatment * is_treated) : log_newcomers +\n", "    (is_post_treatment * is_treated) : project_main_language +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "vif_optimized <- car::vif(model_optimized)\n", "print(vif_optimized)\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 58 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ (is_post_treatment * is_treated):log_project_commits_before_treatment +  \n", "    (is_post_treatment * is_treated):log_project_contributors_before_treatment +  \n", "    (is_post_treatment * is_treated):log_project_age_before_treatment +  \n", "    (is_post_treatment * is_treated):growth_phase + (is_post_treatment *  \n", "    is_treated):log_newcomers + (is_post_treatment * is_treated):project_main_language +  \n", "    log_project_commits + log_project_contributors + log_project_age +  \n", "    (1 | time_cohort_effect) + (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6248466  6249263 -3124172  6248344  3482172 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8094 -0.5016 -0.1651  0.5265  8.7274 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.30076  0.5484  \n", " time_cohort_effect (Intercept) 0.02444  0.1563  \n", " Residual                       0.30190  0.5495  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             1.067e-01\n", "log_project_commits                                                     1.584e-01\n", "log_project_contributors                                                3.182e-01\n", "log_project_age                                                        -2.083e-01\n", "is_post_treatment:log_project_commits_before_treatment                 -3.769e-03\n", "is_treated:log_project_commits_before_treatment                         6.876e-02\n", "is_post_treatment:log_project_contributors_before_treatment            -5.497e-03\n", "is_treated:log_project_contributors_before_treatment                   -2.047e-01\n", "is_post_treatment:log_project_age_before_treatment                      1.871e-02\n", "is_treated:log_project_age_before_treatment                             4.549e-02\n", "is_post_treatment:growth_phaseaccelerating                             -1.407e-01\n", "is_post_treatment:growth_phasedecelerating                             -1.240e-01\n", "is_post_treatment:growth_phasefirst 3 months                           -6.553e-02\n", "is_post_treatment:growth_phasesaturation                               -1.080e-01\n", "is_post_treatment:growth_phasesteady                                   -1.255e-01\n", "is_treated:growth_phaseaccelerating                                    -1.628e-01\n", "is_treated:growth_phasedecelerating                                    -5.183e-01\n", "is_treated:growth_phasefirst 3 months                                   5.290e-01\n", "is_treated:growth_phasesaturation                                      -7.212e-01\n", "is_treated:growth_phasesteady                                          -4.403e-01\n", "is_post_treatment:log_newcomers                                         3.984e-03\n", "is_treated:log_newcomers                                                1.152e-01\n", "is_post_treatment:project_main_languageC#                               1.305e-02\n", "is_post_treatment:project_main_languageC++                              2.931e-03\n", "is_post_treatment:project_main_languageGo                               6.922e-03\n", "is_post_treatment:project_main_languageJava                             9.816e-03\n", "is_post_treatment:project_main_languageJavaScript                       7.006e-03\n", "is_post_treatment:project_main_languagePHP                              1.473e-02\n", "is_post_treatment:project_main_languagePython                           6.945e-03\n", "is_post_treatment:project_main_languageRust                             1.608e-02\n", "is_post_treatment:project_main_languageTypeScript                       1.696e-02\n", "is_treated:project_main_languageC#                                      2.060e-01\n", "is_treated:project_main_languageC++                                     9.319e-02\n", "is_treated:project_main_languageGo                                      3.963e-01\n", "is_treated:project_main_languageJava                                    1.774e-01\n", "is_treated:project_main_languageJavaScript                              1.478e-01\n", "is_treated:project_main_languagePHP                                     7.947e-02\n", "is_treated:project_main_languagePython                                  1.478e-01\n", "is_treated:project_main_languageRust                                    2.716e-01\n", "is_treated:project_main_languageTypeScript                              3.839e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      -1.174e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -1.828e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           2.048e-02\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -3.290e-01\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -2.018e-01\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -9.989e-01\n", "is_post_treatment:is_treated:growth_phasesaturation                    -2.114e-01\n", "is_post_treatment:is_treated:growth_phasesteady                        -2.236e-01\n", "is_post_treatment:is_treated:log_newcomers                              7.846e-02\n", "is_post_treatment:is_treated:project_main_languageC#                   -3.805e-02\n", "is_post_treatment:is_treated:project_main_languageC++                  -1.372e-02\n", "is_post_treatment:is_treated:project_main_languageGo                   -4.966e-02\n", "is_post_treatment:is_treated:project_main_languageJava                 -2.968e-02\n", "is_post_treatment:is_treated:project_main_languageJavaScript           -3.835e-02\n", "is_post_treatment:is_treated:project_main_languagePHP                  -3.584e-02\n", "is_post_treatment:is_treated:project_main_languagePython               -3.718e-02\n", "is_post_treatment:is_treated:project_main_languageRust                 -7.452e-02\n", "is_post_treatment:is_treated:project_main_languageTypeScript           -5.522e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             1.354e-02\n", "log_project_commits                                                     1.712e-03\n", "log_project_contributors                                                2.461e-03\n", "log_project_age                                                         2.219e-03\n", "is_post_treatment:log_project_commits_before_treatment                  1.192e-03\n", "is_treated:log_project_commits_before_treatment                         2.881e-03\n", "is_post_treatment:log_project_contributors_before_treatment             1.830e-03\n", "is_treated:log_project_contributors_before_treatment                    4.333e-03\n", "is_post_treatment:log_project_age_before_treatment                      1.777e-03\n", "is_treated:log_project_age_before_treatment                             4.194e-03\n", "is_post_treatment:growth_phaseaccelerating                              1.226e-02\n", "is_post_treatment:growth_phasedecelerating                              1.239e-02\n", "is_post_treatment:growth_phasefirst 3 months                            6.006e-02\n", "is_post_treatment:growth_phasesaturation                                1.315e-02\n", "is_post_treatment:growth_phasesteady                                    1.233e-02\n", "is_treated:growth_phaseaccelerating                                     2.803e-02\n", "is_treated:growth_phasedecelerating                                     2.824e-02\n", "is_treated:growth_phasefirst 3 months                                   1.184e-01\n", "is_treated:growth_phasesaturation                                       2.950e-02\n", "is_treated:growth_phasesteady                                           2.812e-02\n", "is_post_treatment:log_newcomers                                         1.160e-03\n", "is_treated:log_newcomers                                                2.261e-03\n", "is_post_treatment:project_main_languageC#                               7.054e-03\n", "is_post_treatment:project_main_languageC++                              6.147e-03\n", "is_post_treatment:project_main_languageGo                               6.039e-03\n", "is_post_treatment:project_main_languageJava                             5.969e-03\n", "is_post_treatment:project_main_languageJavaScript                       5.428e-03\n", "is_post_treatment:project_main_languagePHP                              6.740e-03\n", "is_post_treatment:project_main_languagePython                           5.296e-03\n", "is_post_treatment:project_main_languageRust                             7.544e-03\n", "is_post_treatment:project_main_languageTypeScript                       5.917e-03\n", "is_treated:project_main_languageC#                                      1.374e-02\n", "is_treated:project_main_languageC++                                     1.197e-02\n", "is_treated:project_main_languageGo                                      1.176e-02\n", "is_treated:project_main_languageJava                                    1.162e-02\n", "is_treated:project_main_languageJavaScript                              1.056e-02\n", "is_treated:project_main_languagePHP                                     1.311e-02\n", "is_treated:project_main_languagePython                                  1.031e-02\n", "is_treated:project_main_languageRust                                    1.469e-02\n", "is_treated:project_main_languageTypeScript                              1.152e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.224e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.880e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.852e-03\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   1.273e-02\n", "is_post_treatment:is_treated:growth_phasedecelerating                   1.286e-02\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 6.160e-02\n", "is_post_treatment:is_treated:growth_phasesaturation                     1.364e-02\n", "is_post_treatment:is_treated:growth_phasesteady                         1.279e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.192e-03\n", "is_post_treatment:is_treated:project_main_languageC#                    7.237e-03\n", "is_post_treatment:is_treated:project_main_languageC++                   6.307e-03\n", "is_post_treatment:is_treated:project_main_languageGo                    6.193e-03\n", "is_post_treatment:is_treated:project_main_languageJava                  6.125e-03\n", "is_post_treatment:is_treated:project_main_languageJavaScript            5.572e-03\n", "is_post_treatment:is_treated:project_main_languagePHP                   6.915e-03\n", "is_post_treatment:is_treated:project_main_languagePython                5.433e-03\n", "is_post_treatment:is_treated:project_main_languageRust                  7.735e-03\n", "is_post_treatment:is_treated:project_main_languageTypeScript            6.070e-03\n", "                                                                               df\n", "(Intercept)                                                             3.374e+05\n", "log_project_commits                                                     2.058e+05\n", "log_project_contributors                                                2.252e+05\n", "log_project_age                                                         3.513e+05\n", "is_post_treatment:log_project_commits_before_treatment                  1.228e+05\n", "is_treated:log_project_commits_before_treatment                         1.588e+05\n", "is_post_treatment:log_project_contributors_before_treatment             1.230e+05\n", "is_treated:log_project_contributors_before_treatment                    1.625e+05\n", "is_post_treatment:log_project_age_before_treatment                      1.232e+05\n", "is_treated:log_project_age_before_treatment                             1.788e+05\n", "is_post_treatment:growth_phaseaccelerating                              1.235e+05\n", "is_post_treatment:growth_phasedecelerating                              1.235e+05\n", "is_post_treatment:growth_phasefirst 3 months                            1.221e+05\n", "is_post_treatment:growth_phasesaturation                                1.235e+05\n", "is_post_treatment:growth_phasesteady                                    1.235e+05\n", "is_treated:growth_phaseaccelerating                                     1.717e+05\n", "is_treated:growth_phasedecelerating                                     1.712e+05\n", "is_treated:growth_phasefirst 3 months                                   1.439e+05\n", "is_treated:growth_phasesaturation                                       1.687e+05\n", "is_treated:growth_phasesteady                                           1.713e+05\n", "is_post_treatment:log_newcomers                                         1.229e+05\n", "is_treated:log_newcomers                                                1.424e+05\n", "is_post_treatment:project_main_languageC#                               1.234e+05\n", "is_post_treatment:project_main_languageC++                              1.234e+05\n", "is_post_treatment:project_main_languageGo                               1.233e+05\n", "is_post_treatment:project_main_languageJava                             1.234e+05\n", "is_post_treatment:project_main_languageJavaScript                       1.235e+05\n", "is_post_treatment:project_main_languagePHP                              1.236e+05\n", "is_post_treatment:project_main_languagePython                           1.235e+05\n", "is_post_treatment:project_main_languageRust                             1.235e+05\n", "is_post_treatment:project_main_languageTypeScript                       1.234e+05\n", "is_treated:project_main_languageC#                                      1.423e+05\n", "is_treated:project_main_languageC++                                     1.423e+05\n", "is_treated:project_main_languageGo                                      1.423e+05\n", "is_treated:project_main_languageJava                                    1.423e+05\n", "is_treated:project_main_languageJavaScript                              1.423e+05\n", "is_treated:project_main_languagePHP                                     1.423e+05\n", "is_treated:project_main_languagePython                                  1.423e+05\n", "is_treated:project_main_languageRust                                    1.423e+05\n", "is_treated:project_main_languageTypeScript                              1.424e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       3.374e+06\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  3.377e+06\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.420e+06\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   3.410e+06\n", "is_post_treatment:is_treated:growth_phasedecelerating                   3.410e+06\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 3.366e+06\n", "is_post_treatment:is_treated:growth_phasesaturation                     3.411e+06\n", "is_post_treatment:is_treated:growth_phasesteady                         3.409e+06\n", "is_post_treatment:is_treated:log_newcomers                              3.390e+06\n", "is_post_treatment:is_treated:project_main_languageC#                    3.375e+06\n", "is_post_treatment:is_treated:project_main_languageC++                   3.375e+06\n", "is_post_treatment:is_treated:project_main_languageGo                    3.374e+06\n", "is_post_treatment:is_treated:project_main_languageJava                  3.375e+06\n", "is_post_treatment:is_treated:project_main_languageJavaScript            3.378e+06\n", "is_post_treatment:is_treated:project_main_languagePHP                   3.377e+06\n", "is_post_treatment:is_treated:project_main_languagePython                3.375e+06\n", "is_post_treatment:is_treated:project_main_languageRust                  3.373e+06\n", "is_post_treatment:is_treated:project_main_languageTypeScript            3.375e+06\n", "                                                                       t value\n", "(Intercept)                                                              7.881\n", "log_project_commits                                                     92.551\n", "log_project_contributors                                               129.307\n", "log_project_age                                                        -93.841\n", "is_post_treatment:log_project_commits_before_treatment                  -3.162\n", "is_treated:log_project_commits_before_treatment                         23.867\n", "is_post_treatment:log_project_contributors_before_treatment             -3.003\n", "is_treated:log_project_contributors_before_treatment                   -47.247\n", "is_post_treatment:log_project_age_before_treatment                      10.531\n", "is_treated:log_project_age_before_treatment                             10.847\n", "is_post_treatment:growth_phaseaccelerating                             -11.479\n", "is_post_treatment:growth_phasedecelerating                             -10.008\n", "is_post_treatment:growth_phasefirst 3 months                            -1.091\n", "is_post_treatment:growth_phasesaturation                                -8.211\n", "is_post_treatment:growth_phasesteady                                   -10.181\n", "is_treated:growth_phaseaccelerating                                     -5.810\n", "is_treated:growth_phasedecelerating                                    -18.357\n", "is_treated:growth_phasefirst 3 months                                    4.467\n", "is_treated:growth_phasesaturation                                      -24.446\n", "is_treated:growth_phasesteady                                          -15.659\n", "is_post_treatment:log_newcomers                                          3.435\n", "is_treated:log_newcomers                                                50.975\n", "is_post_treatment:project_main_languageC#                                1.851\n", "is_post_treatment:project_main_languageC++                               0.477\n", "is_post_treatment:project_main_languageGo                                1.146\n", "is_post_treatment:project_main_languageJava                              1.644\n", "is_post_treatment:project_main_languageJavaScript                        1.291\n", "is_post_treatment:project_main_languagePHP                               2.186\n", "is_post_treatment:project_main_languagePython                            1.311\n", "is_post_treatment:project_main_languageRust                              2.132\n", "is_post_treatment:project_main_languageTypeScript                        2.867\n", "is_treated:project_main_languageC#                                      15.002\n", "is_treated:project_main_languageC++                                      7.786\n", "is_treated:project_main_languageGo                                      33.711\n", "is_treated:project_main_languageJava                                    15.269\n", "is_treated:project_main_languageJavaScript                              13.990\n", "is_treated:project_main_languagePHP                                      6.061\n", "is_treated:project_main_languagePython                                  14.342\n", "is_treated:project_main_languageRust                                    18.497\n", "is_treated:project_main_languageTypeScript                              33.332\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       -0.959\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -9.722\n", "is_post_treatment:is_treated:log_project_age_before_treatment           11.060\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -25.849\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -15.694\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -16.217\n", "is_post_treatment:is_treated:growth_phasesaturation                    -15.502\n", "is_post_treatment:is_treated:growth_phasesteady                        -17.481\n", "is_post_treatment:is_treated:log_newcomers                              65.802\n", "is_post_treatment:is_treated:project_main_languageC#                    -5.257\n", "is_post_treatment:is_treated:project_main_languageC++                   -2.175\n", "is_post_treatment:is_treated:project_main_languageGo                    -8.019\n", "is_post_treatment:is_treated:project_main_languageJava                  -4.846\n", "is_post_treatment:is_treated:project_main_languageJavaScript            -6.884\n", "is_post_treatment:is_treated:project_main_languagePHP                   -5.183\n", "is_post_treatment:is_treated:project_main_languagePython                -6.844\n", "is_post_treatment:is_treated:project_main_languageRust                  -9.635\n", "is_post_treatment:is_treated:project_main_languageTypeScript            -9.098\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                            3.27e-15\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:log_project_commits_before_treatment                 0.001569\n", "is_treated:log_project_commits_before_treatment                         < 2e-16\n", "is_post_treatment:log_project_contributors_before_treatment            0.002672\n", "is_treated:log_project_contributors_before_treatment                    < 2e-16\n", "is_post_treatment:log_project_age_before_treatment                      < 2e-16\n", "is_treated:log_project_age_before_treatment                             < 2e-16\n", "is_post_treatment:growth_phaseaccelerating                              < 2e-16\n", "is_post_treatment:growth_phasedecelerating                              < 2e-16\n", "is_post_treatment:growth_phasefirst 3 months                           0.275219\n", "is_post_treatment:growth_phasesaturation                                < 2e-16\n", "is_post_treatment:growth_phasesteady                                    < 2e-16\n", "is_treated:growth_phaseaccelerating                                    6.26e-09\n", "is_treated:growth_phasedecelerating                                     < 2e-16\n", "is_treated:growth_phasefirst 3 months                                  7.92e-06\n", "is_treated:growth_phasesaturation                                       < 2e-16\n", "is_treated:growth_phasesteady                                           < 2e-16\n", "is_post_treatment:log_newcomers                                        0.000593\n", "is_treated:log_newcomers                                                < 2e-16\n", "is_post_treatment:project_main_languageC#                              0.064225\n", "is_post_treatment:project_main_languageC++                             0.633504\n", "is_post_treatment:project_main_languageGo                              0.251701\n", "is_post_treatment:project_main_languageJava                            0.100101\n", "is_post_treatment:project_main_languageJavaScript                      0.196825\n", "is_post_treatment:project_main_languagePHP                             0.028844\n", "is_post_treatment:project_main_languagePython                          0.189758\n", "is_post_treatment:project_main_languageRust                            0.033006\n", "is_post_treatment:project_main_languageTypeScript                      0.004149\n", "is_treated:project_main_languageC#                                      < 2e-16\n", "is_treated:project_main_languageC++                                    6.95e-15\n", "is_treated:project_main_languageGo                                      < 2e-16\n", "is_treated:project_main_languageJava                                    < 2e-16\n", "is_treated:project_main_languageJavaScript                              < 2e-16\n", "is_treated:project_main_languagePHP                                    1.36e-09\n", "is_treated:project_main_languagePython                                  < 2e-16\n", "is_treated:project_main_languageRust                                    < 2e-16\n", "is_treated:project_main_languageTypeScript                              < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.337489\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasedecelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 < 2e-16\n", "is_post_treatment:is_treated:growth_phasesaturation                     < 2e-16\n", "is_post_treatment:is_treated:growth_phasesteady                         < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:project_main_languageC#                   1.46e-07\n", "is_post_treatment:is_treated:project_main_languageC++                  0.029621\n", "is_post_treatment:is_treated:project_main_languageGo                   1.07e-15\n", "is_post_treatment:is_treated:project_main_languageJava                 1.26e-06\n", "is_post_treatment:is_treated:project_main_languageJavaScript           5.83e-12\n", "is_post_treatment:is_treated:project_main_languagePHP                  2.19e-07\n", "is_post_treatment:is_treated:project_main_languagePython               7.70e-12\n", "is_post_treatment:is_treated:project_main_languageRust                  < 2e-16\n", "is_post_treatment:is_treated:project_main_languageTypeScript            < 2e-16\n", "                                                                          \n", "(Intercept)                                                            ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:log_project_commits_before_treatment                 ** \n", "is_treated:log_project_commits_before_treatment                        ***\n", "is_post_treatment:log_project_contributors_before_treatment            ** \n", "is_treated:log_project_contributors_before_treatment                   ***\n", "is_post_treatment:log_project_age_before_treatment                     ***\n", "is_treated:log_project_age_before_treatment                            ***\n", "is_post_treatment:growth_phaseaccelerating                             ***\n", "is_post_treatment:growth_phasedecelerating                             ***\n", "is_post_treatment:growth_phasefirst 3 months                              \n", "is_post_treatment:growth_phasesaturation                               ***\n", "is_post_treatment:growth_phasesteady                                   ***\n", "is_treated:growth_phaseaccelerating                                    ***\n", "is_treated:growth_phasedecelerating                                    ***\n", "is_treated:growth_phasefirst 3 months                                  ***\n", "is_treated:growth_phasesaturation                                      ***\n", "is_treated:growth_phasesteady                                          ***\n", "is_post_treatment:log_newcomers                                        ***\n", "is_treated:log_newcomers                                               ***\n", "is_post_treatment:project_main_languageC#                              .  \n", "is_post_treatment:project_main_languageC++                                \n", "is_post_treatment:project_main_languageGo                                 \n", "is_post_treatment:project_main_languageJava                               \n", "is_post_treatment:project_main_languageJavaScript                         \n", "is_post_treatment:project_main_languagePHP                             *  \n", "is_post_treatment:project_main_languagePython                             \n", "is_post_treatment:project_main_languageRust                            *  \n", "is_post_treatment:project_main_languageTypeScript                      ** \n", "is_treated:project_main_languageC#                                     ***\n", "is_treated:project_main_languageC++                                    ***\n", "is_treated:project_main_languageGo                                     ***\n", "is_treated:project_main_languageJava                                   ***\n", "is_treated:project_main_languageJavaScript                             ***\n", "is_treated:project_main_languagePHP                                    ***\n", "is_treated:project_main_languagePython                                 ***\n", "is_treated:project_main_languageRust                                   ***\n", "is_treated:project_main_languageTypeScript                             ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  ***\n", "is_post_treatment:is_treated:growth_phasedecelerating                  ***\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                ***\n", "is_post_treatment:is_treated:growth_phasesaturation                    ***\n", "is_post_treatment:is_treated:growth_phasesteady                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:project_main_languageC#                   ***\n", "is_post_treatment:is_treated:project_main_languageC++                  *  \n", "is_post_treatment:is_treated:project_main_languageGo                   ***\n", "is_post_treatment:is_treated:project_main_languageJava                 ***\n", "is_post_treatment:is_treated:project_main_languageJavaScript           ***\n", "is_post_treatment:is_treated:project_main_languagePHP                  ***\n", "is_post_treatment:is_treated:project_main_languagePython               ***\n", "is_post_treatment:is_treated:project_main_languageRust                 ***\n", "is_post_treatment:is_treated:project_main_languageTypeScript           ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model failed to converge with max|grad| = 0.0212705 (tol = 0.002, component 1)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["summary(model_optimized)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["model_optimized <- lmer(\n", "  log_pr_throughput ~ \n", "    (is_post_treatment * is_treated) : log_project_commits_before_treatment +\n", "    (is_post_treatment * is_treated) : log_project_contributors_before_treatment +\n", "    (is_post_treatment * is_treated) : log_project_age_before_treatment +\n", "    (is_post_treatment * is_treated) : growth_phase +\n", "    (is_post_treatment * is_treated) : log_newcomers +\n", "    (is_post_treatment * is_treated) : project_main_language +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}], "source": ["model_base <- lmer(\n", "  log_pr_throughput ~ \n", "    is_post_treatment + \n", "    is_treated +\n", "    log_project_commits + \n", "    log_project_contributors + \n", "    log_project_age +\n", "    growth_phase +\n", "    log_newcomers +\n", "    project_main_language +\n", "    (1 | time_cohort_effect) + \n", "    (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model failed to converge with max|grad| = 0.00323026 (tol = 0.002, component 1)”\n", "Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                                                             GVIF\n", "is_post_treatment                                                        1.377331\n", "is_treated                                                               1.068431\n", "log_project_commits                                                      2.062160\n", "log_project_contributors                                                 2.417909\n", "log_project_age                                                          1.554111\n", "growth_phase                                                             1.191263\n", "log_newcomers                                                            1.232362\n", "project_main_language                                                    1.336960\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       46.640665\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  32.411503\n", "is_post_treatment:is_treated:log_project_age_before_treatment          114.899598\n", "is_post_treatment:is_treated:growth_phase                              137.718481\n", "is_post_treatment:is_treated:log_newcomers                               3.020082\n", "is_post_treatment:is_treated:project_main_language                      23.590109\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "growth_phase                                                            5\n", "log_newcomers                                                           1\n", "project_main_language                                                   9\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.173597\n", "is_treated                                                                    1.033649\n", "log_project_commits                                                           1.436022\n", "log_project_contributors                                                      1.554963\n", "log_project_age                                                               1.246640\n", "growth_phase                                                                  1.017655\n", "log_newcomers                                                                 1.110118\n", "project_main_language                                                         1.016264\n", "is_post_treatment:is_treated:log_project_commits_before_treatment             6.829397\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        5.693110\n", "is_post_treatment:is_treated:log_project_age_before_treatment                10.719123\n", "is_post_treatment:is_treated:growth_phase                                     1.636437\n", "is_post_treatment:is_treated:log_newcomers                                    1.737838\n", "is_post_treatment:is_treated:project_main_language                            1.191963\n"]}], "source": ["model_interaction <- update(\n", "  model_base,\n", "  . ~ . + \n", "    is_post_treatment:is_treated:log_project_commits_before_treatment +\n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +\n", "    is_post_treatment:is_treated:log_project_age_before_treatment +\n", "    is_post_treatment:is_treated:growth_phase +\n", "    is_post_treatment:is_treated:log_newcomers +\n", "    is_post_treatment:is_treated:project_main_language\n", ")\n", "\n", "# 检查GVIF\n", "vif_interaction <- car::vif(model_interaction)\n", "print(vif_interaction)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 39 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + log_project_commits +  \n", "    log_project_contributors + log_project_age + growth_phase +  \n", "    log_newcomers + project_main_language + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect) + is_post_treatment:is_treated:log_project_commits_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_contributors_before_treatment +  \n", "    is_post_treatment:is_treated:log_project_age_before_treatment +  \n", "    is_post_treatment:is_treated:growth_phase + is_post_treatment:is_treated:log_newcomers +  \n", "    is_post_treatment:is_treated:project_main_language\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6240312  6240860 -3120114  6240228  3482191 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8112 -0.5016 -0.1642  0.5263  8.7630 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.26796  0.5177  \n", " time_cohort_effect (Intercept) 0.02538  0.1593  \n", " Residual                       0.30234  0.5499  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                            -4.033e-01\n", "is_post_treatment                                                      -2.005e-02\n", "is_treated                                                             -5.499e-02\n", "log_project_commits                                                     1.620e-01\n", "log_project_contributors                                                2.171e-01\n", "log_project_age                                                        -1.708e-01\n", "growth_phaseaccelerating                                                4.673e-01\n", "growth_phasedecelerating                                                2.198e-01\n", "growth_phasefirst 3 months                                              8.600e-01\n", "growth_phasesaturation                                                  7.510e-02\n", "growth_phasesteady                                                      2.570e-01\n", "log_newcomers                                                           1.423e-01\n", "project_main_languageC#                                                 1.543e-01\n", "project_main_languageC++                                                1.168e-01\n", "project_main_languageGo                                                 2.744e-01\n", "project_main_languageJava                                               1.439e-01\n", "project_main_languageJavaScript                                         5.974e-02\n", "project_main_languagePHP                                                3.128e-02\n", "project_main_languagePython                                             7.896e-02\n", "project_main_languageRust                                               1.602e-01\n", "project_main_languageTypeScript                                         2.912e-01\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       3.269e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -3.582e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           3.366e-02\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -4.035e-01\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -2.828e-01\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -9.481e-01\n", "is_post_treatment:is_treated:growth_phasesaturation                    -2.905e-01\n", "is_post_treatment:is_treated:growth_phasesteady                        -3.003e-01\n", "is_post_treatment:is_treated:log_newcomers                              8.230e-02\n", "is_post_treatment:is_treated:project_main_languageC#                   -2.509e-02\n", "is_post_treatment:is_treated:project_main_languageC++                  -1.452e-02\n", "is_post_treatment:is_treated:project_main_languageGo                   -3.086e-02\n", "is_post_treatment:is_treated:project_main_languageJava                 -2.048e-02\n", "is_post_treatment:is_treated:project_main_languageJavaScript           -2.424e-02\n", "is_post_treatment:is_treated:project_main_languagePHP                  -2.258e-02\n", "is_post_treatment:is_treated:project_main_languagePython               -2.550e-02\n", "is_post_treatment:is_treated:project_main_languageRust                 -5.229e-02\n", "is_post_treatment:is_treated:project_main_languageTypeScript           -3.428e-02\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             2.767e-01\n", "is_post_treatment                                                       1.217e-03\n", "is_treated                                                              2.926e-03\n", "log_project_commits                                                     1.306e-03\n", "log_project_contributors                                                1.922e-03\n", "log_project_age                                                         1.761e-03\n", "growth_phaseaccelerating                                                2.764e-01\n", "growth_phasedecelerating                                                2.764e-01\n", "growth_phasefirst 3 months                                              2.871e-01\n", "growth_phasesaturation                                                  2.764e-01\n", "growth_phasesteady                                                      2.764e-01\n", "log_newcomers                                                           1.367e-03\n", "project_main_languageC#                                                 9.241e-03\n", "project_main_languageC++                                                8.050e-03\n", "project_main_languageGo                                                 7.848e-03\n", "project_main_languageJava                                               7.823e-03\n", "project_main_languageJavaScript                                         7.051e-03\n", "project_main_languagePHP                                                8.807e-03\n", "project_main_languagePython                                             6.919e-03\n", "project_main_languageRust                                               9.836e-03\n", "project_main_languageTypeScript                                         7.717e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.050e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.609e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.553e-03\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   1.076e-02\n", "is_post_treatment:is_treated:growth_phasedecelerating                   1.087e-02\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 5.316e-02\n", "is_post_treatment:is_treated:growth_phasesaturation                     1.154e-02\n", "is_post_treatment:is_treated:growth_phasesteady                         1.081e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.023e-03\n", "is_post_treatment:is_treated:project_main_languageC#                    6.244e-03\n", "is_post_treatment:is_treated:project_main_languageC++                   5.441e-03\n", "is_post_treatment:is_treated:project_main_languageGo                    5.341e-03\n", "is_post_treatment:is_treated:project_main_languageJava                  5.284e-03\n", "is_post_treatment:is_treated:project_main_languageJavaScript            4.805e-03\n", "is_post_treatment:is_treated:project_main_languagePHP                   5.963e-03\n", "is_post_treatment:is_treated:project_main_languagePython                4.685e-03\n", "is_post_treatment:is_treated:project_main_languageRust                  6.670e-03\n", "is_post_treatment:is_treated:project_main_languageTypeScript            5.235e-03\n", "                                                                               df\n", "(Intercept)                                                             1.398e+05\n", "is_post_treatment                                                       1.097e+05\n", "is_treated                                                              1.222e+05\n", "log_project_commits                                                     1.747e+05\n", "log_project_contributors                                                1.866e+05\n", "log_project_age                                                         2.420e+05\n", "growth_phaseaccelerating                                                1.397e+05\n", "growth_phasedecelerating                                                1.397e+05\n", "growth_phasefirst 3 months                                              1.403e+05\n", "growth_phasesaturation                                                  1.397e+05\n", "growth_phasesteady                                                      1.397e+05\n", "log_newcomers                                                           1.507e+05\n", "project_main_languageC#                                                 1.477e+05\n", "project_main_languageC++                                                1.477e+05\n", "project_main_languageGo                                                 1.477e+05\n", "project_main_languageJava                                               1.477e+05\n", "project_main_languageJavaScript                                         1.477e+05\n", "project_main_languagePHP                                                1.477e+05\n", "project_main_languagePython                                             1.477e+05\n", "project_main_languageRust                                               1.477e+05\n", "project_main_languageTypeScript                                         1.477e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       8.880e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  8.948e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           9.399e+05\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   9.536e+05\n", "is_post_treatment:is_treated:growth_phasedecelerating                   9.542e+05\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 9.038e+05\n", "is_post_treatment:is_treated:growth_phasesaturation                     9.455e+05\n", "is_post_treatment:is_treated:growth_phasesteady                         9.522e+05\n", "is_post_treatment:is_treated:log_newcomers                              9.030e+05\n", "is_post_treatment:is_treated:project_main_languageC#                    8.791e+05\n", "is_post_treatment:is_treated:project_main_languageC++                   8.789e+05\n", "is_post_treatment:is_treated:project_main_languageGo                    8.800e+05\n", "is_post_treatment:is_treated:project_main_languageJava                  8.789e+05\n", "is_post_treatment:is_treated:project_main_languageJavaScript            8.773e+05\n", "is_post_treatment:is_treated:project_main_languagePHP                   8.751e+05\n", "is_post_treatment:is_treated:project_main_languagePython                8.771e+05\n", "is_post_treatment:is_treated:project_main_languageRust                  8.780e+05\n", "is_post_treatment:is_treated:project_main_languageTypeScript            8.778e+05\n", "                                                                       t value\n", "(Intercept)                                                             -1.458\n", "is_post_treatment                                                      -16.472\n", "is_treated                                                             -18.798\n", "log_project_commits                                                    124.013\n", "log_project_contributors                                               112.964\n", "log_project_age                                                        -97.010\n", "growth_phaseaccelerating                                                 1.691\n", "growth_phasedecelerating                                                 0.795\n", "growth_phasefirst 3 months                                               2.995\n", "growth_phasesaturation                                                   0.272\n", "growth_phasesteady                                                       0.930\n", "log_newcomers                                                          104.056\n", "project_main_languageC#                                                 16.697\n", "project_main_languageC++                                                14.513\n", "project_main_languageGo                                                 34.960\n", "project_main_languageJava                                               18.397\n", "project_main_languageJavaScript                                          8.473\n", "project_main_languagePHP                                                 3.552\n", "project_main_languagePython                                             11.411\n", "project_main_languageRust                                               16.289\n", "project_main_languageTypeScript                                         37.742\n", "is_post_treatment:is_treated:log_project_commits_before_treatment        3.115\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -22.257\n", "is_post_treatment:is_treated:log_project_age_before_treatment           21.673\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -37.502\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -26.028\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -17.833\n", "is_post_treatment:is_treated:growth_phasesaturation                    -25.179\n", "is_post_treatment:is_treated:growth_phasesteady                        -27.768\n", "is_post_treatment:is_treated:log_newcomers                              80.438\n", "is_post_treatment:is_treated:project_main_languageC#                    -4.019\n", "is_post_treatment:is_treated:project_main_languageC++                   -2.669\n", "is_post_treatment:is_treated:project_main_languageGo                    -5.778\n", "is_post_treatment:is_treated:project_main_languageJava                  -3.877\n", "is_post_treatment:is_treated:project_main_languageJavaScript            -5.045\n", "is_post_treatment:is_treated:project_main_languagePHP                   -3.787\n", "is_post_treatment:is_treated:project_main_languagePython                -5.443\n", "is_post_treatment:is_treated:project_main_languageRust                  -7.840\n", "is_post_treatment:is_treated:project_main_languageTypeScript            -6.549\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                            0.144925\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "growth_phaseaccelerating                                               0.090921\n", "growth_phasedecelerating                                               0.426463\n", "growth_phasefirst 3 months                                             0.002741\n", "growth_phasesaturation                                                 0.785864\n", "growth_phasesteady                                                     0.352433\n", "log_newcomers                                                           < 2e-16\n", "project_main_languageC#                                                 < 2e-16\n", "project_main_languageC++                                                < 2e-16\n", "project_main_languageGo                                                 < 2e-16\n", "project_main_languageJava                                               < 2e-16\n", "project_main_languageJavaScript                                         < 2e-16\n", "project_main_languagePHP                                               0.000383\n", "project_main_languagePython                                             < 2e-16\n", "project_main_languageRust                                               < 2e-16\n", "project_main_languageTypeScript                                         < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      0.001841\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasedecelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 < 2e-16\n", "is_post_treatment:is_treated:growth_phasesaturation                     < 2e-16\n", "is_post_treatment:is_treated:growth_phasesteady                         < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:project_main_languageC#                   5.85e-05\n", "is_post_treatment:is_treated:project_main_languageC++                  0.007604\n", "is_post_treatment:is_treated:project_main_languageGo                   7.56e-09\n", "is_post_treatment:is_treated:project_main_languageJava                 0.000106\n", "is_post_treatment:is_treated:project_main_languageJavaScript           4.53e-07\n", "is_post_treatment:is_treated:project_main_languagePHP                  0.000152\n", "is_post_treatment:is_treated:project_main_languagePython               5.25e-08\n", "is_post_treatment:is_treated:project_main_languageRust                 4.50e-15\n", "is_post_treatment:is_treated:project_main_languageTypeScript           5.79e-11\n", "                                                                          \n", "(Intercept)                                                               \n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "growth_phaseaccelerating                                               .  \n", "growth_phasedecelerating                                                  \n", "growth_phasefirst 3 months                                             ** \n", "growth_phasesaturation                                                    \n", "growth_phasesteady                                                        \n", "log_newcomers                                                          ***\n", "project_main_languageC#                                                ***\n", "project_main_languageC++                                               ***\n", "project_main_languageGo                                                ***\n", "project_main_languageJava                                              ***\n", "project_main_languageJavaScript                                        ***\n", "project_main_languagePHP                                               ***\n", "project_main_languagePython                                            ***\n", "project_main_languageRust                                              ***\n", "project_main_languageTypeScript                                        ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ** \n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  ***\n", "is_post_treatment:is_treated:growth_phasedecelerating                  ***\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                ***\n", "is_post_treatment:is_treated:growth_phasesaturation                    ***\n", "is_post_treatment:is_treated:growth_phasesteady                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:project_main_languageC#                   ***\n", "is_post_treatment:is_treated:project_main_languageC++                  ** \n", "is_post_treatment:is_treated:project_main_languageGo                   ***\n", "is_post_treatment:is_treated:project_main_languageJava                 ***\n", "is_post_treatment:is_treated:project_main_languageJavaScript           ***\n", "is_post_treatment:is_treated:project_main_languagePHP                  ***\n", "is_post_treatment:is_treated:project_main_languagePython               ***\n", "is_post_treatment:is_treated:project_main_languageRust                 ***\n", "is_post_treatment:is_treated:project_main_languageTypeScript           ***\n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model failed to converge with max|grad| = 0.00323026 (tol = 0.002, component 1)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["summary(model_interaction)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message:\n", "“package ‘dplyr’ was built under R version 4.3.3”\n", "\n", "Attaching package: ‘dplyr’\n", "\n", "\n", "The following object is masked from ‘package:car’:\n", "\n", "    recode\n", "\n", "\n", "The following objects are masked from ‘package:stats’:\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from ‘package:base’:\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n"]}], "source": ["library(lme4)      # 混合效应模型\n", "library(lmerTest)  # 提供p值\n", "library(dplyr)     # 数据操作\n", "library(car)       # 共线性诊断\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["# 标准化连续变量（关键步骤：降低共线性）\n", "compiled_data_test_clean <- compiled_data_test %>%\n", "  mutate(\n", "    across(\n", "      c(log_project_commits, log_project_contributors, log_project_age, log_newcomers),\n", "      ~ scale(.) %>% as.numeric()  # 标准化并转换为数值\n", "    )\n", "  ) %>%\n", "  # 移除预处理变量（与处理后变量可能高度相关）\n", "  select(-contains(\"_before_treatment\"))  \n", "\n", "# 确保分类变量为因子\n", "compiled_data_test_clean <- compiled_data_test_clean %>%\n", "  mutate(\n", "    growth_phase = factor(growth_phase),\n", "    project_main_language = factor(project_main_language),\n", "    time_cohort_effect = factor(time_cohort_effect)  # 转换为因子以用作固定效应\n", "  )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["model_base <- lmer(\n", "  log_pr_throughput ~ \n", "    is_post_treatment * is_treated +  # 双重差分核心项\n", "    log_project_commits + \n", "    log_project_contributors +\n", "    log_project_age +\n", "    growth_phase +\n", "    log_newcomers +\n", "    project_main_language +\n", "    (1 | repo_cohort_effect) +        # 仅保留仓库随机效应\n", "    time_cohort_effect,               # 时间固定效应\n", "  data = compiled_data_test_clean,\n", "  control = lmerControl(optimizer = \"bobyqa\")  # 稳定优化器\n", ")\n", "model_language <- update(\n", "  model_base,\n", "  . ~ . + is_post_treatment:is_treated:project_main_language\n", ")\n", "# 共线性诊断\n", "vif_language <- car::vif(model_language)\n", "print(vif_language)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [], "source": ["model_language <- update(\n", "  model_base,\n", "  . ~ . + is_post_treatment:is_treated:project_main_language\n", ")\n", "# 共线性诊断\n", "vif_language <- car::vif(model_language)\n", "print(vif_language)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in checkConv(attr(opt, \"derivs\"), opt$par, ctrl = control$checkConv, :\n", "“Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?”\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                                                                             GVIF\n", "is_post_treatment                                                        1.388002\n", "is_treated                                                               1.060342\n", "log_project_commits                                                      2.000433\n", "log_project_contributors                                                 2.286271\n", "log_project_age                                                          1.454374\n", "is_post_treatment:is_treated:log_tenure                                 47.243194\n", "is_post_treatment:is_treated:log_commit_percent                          5.360273\n", "is_post_treatment:is_treated:log_commits                                47.133848\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      119.153774\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  38.828822\n", "is_post_treatment:is_treated:log_project_age_before_treatment          137.131110\n", "is_post_treatment:is_treated:growth_phase                              132.326009\n", "is_post_treatment:is_treated:log_newcomers                               3.054859\n", "is_post_treatment:is_treated:project_main_language                      18.911149\n", "                                                                       Df\n", "is_post_treatment                                                       1\n", "is_treated                                                              1\n", "log_project_commits                                                     1\n", "log_project_contributors                                                1\n", "log_project_age                                                         1\n", "is_post_treatment:is_treated:log_tenure                                 1\n", "is_post_treatment:is_treated:log_commit_percent                         1\n", "is_post_treatment:is_treated:log_commits                                1\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1\n", "is_post_treatment:is_treated:growth_phase                               5\n", "is_post_treatment:is_treated:log_newcomers                              1\n", "is_post_treatment:is_treated:project_main_language                      9\n", "                                                                       GVIF^(1/(2*Df))\n", "is_post_treatment                                                             1.178135\n", "is_treated                                                                    1.029729\n", "log_project_commits                                                           1.414367\n", "log_project_contributors                                                      1.512042\n", "log_project_age                                                               1.205974\n", "is_post_treatment:is_treated:log_tenure                                       6.873368\n", "is_post_treatment:is_treated:log_commit_percent                               2.315226\n", "is_post_treatment:is_treated:log_commits                                      6.865410\n", "is_post_treatment:is_treated:log_project_commits_before_treatment            10.915758\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment        6.231278\n", "is_post_treatment:is_treated:log_project_age_before_treatment                11.710299\n", "is_post_treatment:is_treated:growth_phase                                     1.629913\n", "is_post_treatment:is_treated:log_newcomers                                    1.747815\n", "is_post_treatment:is_treated:project_main_language                            1.177413\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 27 > 12.\n", "Use print(obj, correlation=TRUE)  or\n", "    vcov(obj)        if you need it\n", "\n", "\n"]}, {"data": {"text/plain": ["Linear mixed model fit by maximum likelihood . t-tests use <PERSON><PERSON><PERSON><PERSON><PERSON>'s\n", "  method [lmerModLmerTest]\n", "Formula: \n", "log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_tenure +  \n", "    is_treated:is_post_treatment:log_commit_percent + is_treated:is_post_treatment:log_commits +  \n", "    is_treated:is_post_treatment:log_project_commits_before_treatment +  \n", "    is_treated:is_post_treatment:log_project_contributors_before_treatment +  \n", "    is_treated:is_post_treatment:log_project_age_before_treatment +  \n", "    is_treated:is_post_treatment:growth_phase + is_treated:is_post_treatment:log_newcomers +  \n", "    is_treated:is_post_treatment:project_main_language + log_project_commits +  \n", "    log_project_contributors + log_project_age + (1 | time_cohort_effect) +  \n", "    (1 | repo_cohort_effect)\n", "   Data: compiled_data_test\n", "\n", "     AIC      BIC   logLik deviance df.resid \n", " 6259485  6259877 -3129713  6259425  3482203 \n", "\n", "Scaled residuals: \n", "    Min      1Q  Median      3Q     Max \n", "-7.8236 -0.5020 -0.1665  0.5266  8.7489 \n", "\n", "Random effects:\n", " Groups             Name        Variance Std.Dev.\n", " repo_cohort_effect (Intercept) 0.32221  0.5676  \n", " time_cohort_effect (Intercept) 0.02488  0.1577  \n", " Residual                       0.30201  0.5496  \n", "Number of obs: 3482233, groups:  \n", "repo_cohort_effect, 139910; time_cohort_effect, 139900\n", "\n", "Fixed effects:\n", "                                                                         Estimate\n", "(Intercept)                                                             2.655e-01\n", "is_post_treatment                                                      -1.886e-02\n", "is_treated                                                             -6.408e-02\n", "log_project_commits                                                     1.790e-01\n", "log_project_contributors                                                2.800e-01\n", "log_project_age                                                        -2.339e-01\n", "is_post_treatment:is_treated:log_tenure                                 3.705e-02\n", "is_post_treatment:is_treated:log_commit_percent                        -7.623e-02\n", "is_post_treatment:is_treated:log_commits                               -1.844e-02\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       8.490e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment -4.926e-02\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.624e-02\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  -4.265e-01\n", "is_post_treatment:is_treated:growth_phasedecelerating                  -3.342e-01\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                -9.437e-01\n", "is_post_treatment:is_treated:growth_phasesaturation                    -3.529e-01\n", "is_post_treatment:is_treated:growth_phasesteady                        -3.493e-01\n", "is_post_treatment:is_treated:log_newcomers                              9.498e-02\n", "is_post_treatment:is_treated:project_main_languageC#                   -1.251e-03\n", "is_post_treatment:is_treated:project_main_languageC++                   5.279e-04\n", "is_post_treatment:is_treated:project_main_languageGo                    2.400e-03\n", "is_post_treatment:is_treated:project_main_languageJava                  1.816e-04\n", "is_post_treatment:is_treated:project_main_languageJavaScript           -5.553e-03\n", "is_post_treatment:is_treated:project_main_languagePHP                  -9.504e-03\n", "is_post_treatment:is_treated:project_main_languagePython               -8.884e-03\n", "is_post_treatment:is_treated:project_main_languageRust                 -2.104e-02\n", "is_post_treatment:is_treated:project_main_languageTypeScript            6.483e-03\n", "                                                                       <PERSON>d<PERSON>\n", "(Intercept)                                                             1.120e-02\n", "is_post_treatment                                                       1.214e-03\n", "is_treated                                                              3.184e-03\n", "log_project_commits                                                     1.383e-03\n", "log_project_contributors                                                2.008e-03\n", "log_project_age                                                         1.804e-03\n", "is_post_treatment:is_treated:log_tenure                                 1.063e-03\n", "is_post_treatment:is_treated:log_commit_percent                         1.010e-02\n", "is_post_treatment:is_treated:log_commits                                1.508e-03\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       1.678e-03\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  1.762e-03\n", "is_post_treatment:is_treated:log_project_age_before_treatment           1.697e-03\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   1.088e-02\n", "is_post_treatment:is_treated:growth_phasedecelerating                   1.104e-02\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 5.258e-02\n", "is_post_treatment:is_treated:growth_phasesaturation                     1.176e-02\n", "is_post_treatment:is_treated:growth_phasesteady                         1.100e-02\n", "is_post_treatment:is_treated:log_newcomers                              1.030e-03\n", "is_post_treatment:is_treated:project_main_languageC#                    6.179e-03\n", "is_post_treatment:is_treated:project_main_languageC++                   5.380e-03\n", "is_post_treatment:is_treated:project_main_languageGo                    5.289e-03\n", "is_post_treatment:is_treated:project_main_languageJava                  5.229e-03\n", "is_post_treatment:is_treated:project_main_languageJavaScript            4.770e-03\n", "is_post_treatment:is_treated:project_main_languagePHP                   5.904e-03\n", "is_post_treatment:is_treated:project_main_languagePython                4.641e-03\n", "is_post_treatment:is_treated:project_main_languageRust                  6.615e-03\n", "is_post_treatment:is_treated:project_main_languageTypeScript            5.193e-03\n", "                                                                               df\n", "(Intercept)                                                             2.487e+05\n", "is_post_treatment                                                       1.157e+05\n", "is_treated                                                              1.255e+05\n", "log_project_commits                                                     1.833e+05\n", "log_project_contributors                                                1.949e+05\n", "log_project_age                                                         2.561e+05\n", "is_post_treatment:is_treated:log_tenure                                 9.215e+05\n", "is_post_treatment:is_treated:log_commit_percent                         9.161e+05\n", "is_post_treatment:is_treated:log_commits                                9.269e+05\n", "is_post_treatment:is_treated:log_project_commits_before_treatment       9.133e+05\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  9.021e+05\n", "is_post_treatment:is_treated:log_project_age_before_treatment           9.478e+05\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   9.666e+05\n", "is_post_treatment:is_treated:growth_phasedecelerating                   9.656e+05\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 9.422e+05\n", "is_post_treatment:is_treated:growth_phasesaturation                     9.582e+05\n", "is_post_treatment:is_treated:growth_phasesteady                         9.635e+05\n", "is_post_treatment:is_treated:log_newcomers                              9.341e+05\n", "is_post_treatment:is_treated:project_main_languageC#                    9.193e+05\n", "is_post_treatment:is_treated:project_main_languageC++                   9.192e+05\n", "is_post_treatment:is_treated:project_main_languageGo                    9.196e+05\n", "is_post_treatment:is_treated:project_main_languageJava                  9.192e+05\n", "is_post_treatment:is_treated:project_main_languageJavaScript            9.168e+05\n", "is_post_treatment:is_treated:project_main_languagePHP                   9.154e+05\n", "is_post_treatment:is_treated:project_main_languagePython                9.172e+05\n", "is_post_treatment:is_treated:project_main_languageRust                  9.179e+05\n", "is_post_treatment:is_treated:project_main_languageTypeScript            9.178e+05\n", "                                                                        t value\n", "(Intercept)                                                              23.706\n", "is_post_treatment                                                       -15.540\n", "is_treated                                                              -20.126\n", "log_project_commits                                                     129.404\n", "log_project_contributors                                                139.443\n", "log_project_age                                                        -129.614\n", "is_post_treatment:is_treated:log_tenure                                  34.861\n", "is_post_treatment:is_treated:log_commit_percent                          -7.545\n", "is_post_treatment:is_treated:log_commits                                -12.231\n", "is_post_treatment:is_treated:log_project_commits_before_treatment         5.058\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  -27.953\n", "is_post_treatment:is_treated:log_project_age_before_treatment             9.568\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   -39.220\n", "is_post_treatment:is_treated:growth_phasedecelerating                   -30.265\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 -17.948\n", "is_post_treatment:is_treated:growth_phasesaturation                     -29.998\n", "is_post_treatment:is_treated:growth_phasesteady                         -31.767\n", "is_post_treatment:is_treated:log_newcomers                               92.177\n", "is_post_treatment:is_treated:project_main_languageC#                     -0.202\n", "is_post_treatment:is_treated:project_main_languageC++                     0.098\n", "is_post_treatment:is_treated:project_main_languageGo                      0.454\n", "is_post_treatment:is_treated:project_main_languageJava                    0.035\n", "is_post_treatment:is_treated:project_main_languageJavaScript             -1.164\n", "is_post_treatment:is_treated:project_main_languagePHP                    -1.610\n", "is_post_treatment:is_treated:project_main_languagePython                 -1.914\n", "is_post_treatment:is_treated:project_main_languageRust                   -3.181\n", "is_post_treatment:is_treated:project_main_languageTypeScript              1.248\n", "                                                                       Pr(>|t|)\n", "(Intercept)                                                             < 2e-16\n", "is_post_treatment                                                       < 2e-16\n", "is_treated                                                              < 2e-16\n", "log_project_commits                                                     < 2e-16\n", "log_project_contributors                                                < 2e-16\n", "log_project_age                                                         < 2e-16\n", "is_post_treatment:is_treated:log_tenure                                 < 2e-16\n", "is_post_treatment:is_treated:log_commit_percent                        4.53e-14\n", "is_post_treatment:is_treated:log_commits                                < 2e-16\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      4.23e-07\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment  < 2e-16\n", "is_post_treatment:is_treated:log_project_age_before_treatment           < 2e-16\n", "is_post_treatment:is_treated:growth_phaseaccelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasedecelerating                   < 2e-16\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                 < 2e-16\n", "is_post_treatment:is_treated:growth_phasesaturation                     < 2e-16\n", "is_post_treatment:is_treated:growth_phasesteady                         < 2e-16\n", "is_post_treatment:is_treated:log_newcomers                              < 2e-16\n", "is_post_treatment:is_treated:project_main_languageC#                    0.83960\n", "is_post_treatment:is_treated:project_main_languageC++                   0.92183\n", "is_post_treatment:is_treated:project_main_languageGo                    0.65001\n", "is_post_treatment:is_treated:project_main_languageJava                  0.97229\n", "is_post_treatment:is_treated:project_main_languageJavaScript            0.24433\n", "is_post_treatment:is_treated:project_main_languagePHP                   0.10745\n", "is_post_treatment:is_treated:project_main_languagePython                0.05560\n", "is_post_treatment:is_treated:project_main_languageRust                  0.00147\n", "is_post_treatment:is_treated:project_main_languageTypeScript            0.21188\n", "                                                                          \n", "(Intercept)                                                            ***\n", "is_post_treatment                                                      ***\n", "is_treated                                                             ***\n", "log_project_commits                                                    ***\n", "log_project_contributors                                               ***\n", "log_project_age                                                        ***\n", "is_post_treatment:is_treated:log_tenure                                ***\n", "is_post_treatment:is_treated:log_commit_percent                        ***\n", "is_post_treatment:is_treated:log_commits                               ***\n", "is_post_treatment:is_treated:log_project_commits_before_treatment      ***\n", "is_post_treatment:is_treated:log_project_contributors_before_treatment ***\n", "is_post_treatment:is_treated:log_project_age_before_treatment          ***\n", "is_post_treatment:is_treated:growth_phaseaccelerating                  ***\n", "is_post_treatment:is_treated:growth_phasedecelerating                  ***\n", "is_post_treatment:is_treated:growth_phasefirst 3 months                ***\n", "is_post_treatment:is_treated:growth_phasesaturation                    ***\n", "is_post_treatment:is_treated:growth_phasesteady                        ***\n", "is_post_treatment:is_treated:log_newcomers                             ***\n", "is_post_treatment:is_treated:project_main_languageC#                      \n", "is_post_treatment:is_treated:project_main_languageC++                     \n", "is_post_treatment:is_treated:project_main_languageGo                      \n", "is_post_treatment:is_treated:project_main_languageJava                    \n", "is_post_treatment:is_treated:project_main_languageJavaScript              \n", "is_post_treatment:is_treated:project_main_languagePHP                     \n", "is_post_treatment:is_treated:project_main_languagePython               .  \n", "is_post_treatment:is_treated:project_main_languageRust                 ** \n", "is_post_treatment:is_treated:project_main_languageTypeScript              \n", "---\n", "Signif. codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1\n", "optimizer (nloptwrap) convergence code: 0 (OK)\n", "Model is nearly unidentifiable: very large eigenvalue\n", " - Rescale variables?\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A matrix: 1 × 2 of type dbl</caption>\n", "<thead>\n", "\t<tr><th scope=col>R2m</th><th scope=col>R2c</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>0.257267</td><td>0.6544263</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A matrix: 1 × 2 of type dbl\n", "\\begin{tabular}{ll}\n", " R2m & R2c\\\\\n", "\\hline\n", "\t 0.257267 & 0.6544263\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A matrix: 1 × 2 of type dbl\n", "\n", "| R2m | R2c |\n", "|---|---|\n", "| 0.257267 | 0.6544263 |\n", "\n"], "text/plain": ["     R2m      R2c      \n", "[1,] 0.257267 0.6544263"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Model 4: Fixed Effects + Developer Characteristics + Project Characteristics\n", "model_fixed_effects_developer_project <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment:log_tenure + is_treated:is_post_treatment:log_commit_percent + is_treated:is_post_treatment:log_commits +\n", "                     is_treated:is_post_treatment:log_project_commits_before_treatment + is_treated:is_post_treatment:log_project_contributors_before_treatment +\n", "                     is_treated:is_post_treatment:log_project_age_before_treatment + is_treated:is_post_treatment:growth_phase +\n", "                      is_treated:is_post_treatment:log_newcomers +\n", "                     is_treated:is_post_treatment:project_main_language +\n", "                                          log_project_commits + log_project_contributors + log_project_age + \n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_developer_project <- vif(model_fixed_effects_developer_project)\n", "print(vif_model_fixed_effects_developer_project)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_developer_project)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_developer_project)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Correlation matrix not shown by default, as p = 24 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n", "\n", "Correlation matrix not shown by default, as p = 27 > 12.\n", "Use print(summary(model), correlation=TRUE)  or\n", "    vcov(summary(model))        if you need it\n", "\n", "\n"]}], "source": ["# Create a function to export model summaries to text files\n", "export_model_summary <- function(model, filename) {\n", "  sink(file = paste0(\"../result/model_summaries_with_newcomer_num/\", filename, \".txt\"))\n", "  print(summary(model))\n", "  print(r.squaredGLMM(model))\n", "  sink()\n", "}\n", "\n", "# Create directory if it doesn't exist\n", "dir.create(\"../result/model_summaries_with_newcomer_num\", recursive = TRUE, showWarnings = FALSE)\n", "\n", "# Export summaries for the three models\n", "export_model_summary(model_fixed_effects_only, \"model1_fixed_effects_only\")\n", "export_model_summary(model_fixed_effects_developer, \"model2_fixed_effects_developer\")\n", "export_model_summary(model_fixed_effects_project, \"model3_fixed_effects_project\")\n", "export_model_summary(model_fixed_effects_developer_project, \"model4_fixed_effects_developer_project\")\n", "\n", "# Also export VIF results\n", "# Create directory for VIF results\n", "dir.create(\"../result/model_summaries\", recursive = TRUE, showWarnings = FALSE)\n", "\n", "# Export VIF results\n", "sink(file = \"../result/model_summaries/vif_results.txt\")\n", "\n", "cat(\"VIF for Model 1 (Fixed Effects Only):\\n\")\n", "print(vif_model_fixed_effects_only)\n", "cat(\"\\n\\nVIF for Model 2 (Fixed Effects + Developer Characteristics):\\n\")\n", "print(vif_model_fixed_effects_developer)\n", "cat(\"\\n\\nVIF for Model 3 (Fixed Effects + Project Characteristics):\\n\")\n", "print(vif_model_fixed_effects_project)\n", "cat(\"\\n\\nVIF for Model 4 (Fixed Effects + Developer Characteristics + Project Characteristics):\\n\")\n", "print(vif_model_fixed_effects_developer_project)\n", "\n", "\n", "cat(\"\\n\\nVIF Summary:\\n\")\n", "vif_summary <- cbind(VIF = c(vif_model_fixed_effects_only, vif_model_fixed_effects_developer, \n", "                              vif_model_fixed_effects_project, vif_model_fixed_effects_developer_project))\n", "print(vif_summary)\n", "\n", "\n", "sink(file = \"../result/model_summaries/vif_summary.txt\")\n", "print(vif_summary)\n", "sink()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Installing package into ‘/home/<USER>/R/x86_64-pc-linux-gnu-library/4.3’\n", "(as ‘lib’ is unspecified)\n", "\n", "also installing the dependencies ‘estimatr’, ‘pracma’\n", "\n", "\n"]}], "source": ["install.packages(\"eventstudyr\")\n", "library(eventstudyr)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", :\n", "“Dataset is unbalanced.”\n", "Warning message in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", :\n", "“Note: gaps of more than one unit in the time variable 'relativized_time' were detected. Treating these as gaps in the panel dimension.”\n"]}, {"ename": "ERROR", "evalue": "Error in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", : overidpre + pre + post + overidpost cannot exceed the data window.\n", "output_type": "error", "traceback": ["Error in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", : overidpre + pre + post + overidpost cannot exceed the data window.\nTraceback:\n", "1. EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", \n .     policyvar = \"is_treated\", idvar = \"cohort_id\", timevar = \"relativized_time\", \n .     pre = 8, post = 8, normalize = 0)", "2. stop(\"overidpre + pre + post + overidpost cannot exceed the data window.\")"]}], "source": ["# Load required libraries\n", "library(eventstudyr)\n", "library(ggplot2)\n", "\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/did_result_20250212/compiled_data_test.csv\")\n", "compiled_data_test <- compiled_data_test %>%\n", "  group_by(cohort_id) %>%\n", "  mutate(relativized_time = as.integer(relativized_time)) %>%\n", "  ungroup()\n", "# Estimate the event study model without control variables\n", "estimates_without_controls <- EventStudy(\n", "  estimator = \"OLS\",\n", "  data = compiled_data_test,\n", "  outcomevar = \"log_pr_throughput\",\n", "  policyvar = \"is_treated\",\n", "  idvar = \"cohort_id\",\n", "  timevar = \"relativized_time\",\n", "  pre = 8, post = 8,\n", "  normalize = -1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: ‘tidyr’\n", "\n", "\n", "The following objects are masked from ‘package:Matrix’:\n", "\n", "    expand, pack, unpack\n", "\n", "\n"]}, {"ename": "ERROR", "evalue": "\u001b[1m\u001b[33mError\u001b[39m in `fill()`:\u001b[22m\n\u001b[33m!\u001b[39m Can't subset columns that don't exist.\n\u001b[31m✖\u001b[39m Column `log_pr_throughput` doesn't exist.\n", "output_type": "error", "traceback": ["\u001b[1m\u001b[33mError\u001b[39m in `fill()`:\u001b[22m\n\u001b[33m!\u001b[39m Can't subset columns that don't exist.\n\u001b[31m✖\u001b[39m Column `log_pr_throughput` doesn't exist.\nTraceback:\n", "1. compiled_data_test %>% group_by(cohort_id) %>% complete(relativized_time = seq(min(relativized_time), \n .     max(relativized_time), by = 1)) %>% fill(log_pr_throughput, \n .     is_treated, log_tenure, log_commit_percent, log_commits, \n .     .direction = \"down\") %>% ungroup()", "2. ungroup(.)", "3. fill(., log_pr_throughput, is_treated, log_tenure, log_commit_percent, \n .     log_commits, .direction = \"down\")", "4. fill.data.frame(., log_pr_throughput, is_treated, log_tenure, \n .     log_commit_percent, log_commits, .direction = \"down\")", "5. tidyselect::eval_select(expr(c(...)), data, allow_rename = FALSE)", "6. eval_select_impl(data, names(data), as_quosure(expr, env), include = include, \n .     exclude = exclude, strict = strict, name_spec = name_spec, \n .     allow_rename = allow_rename, allow_empty = allow_empty, allow_predicates = allow_predicates, \n .     error_call = error_call, )", "7. with_subscript_errors(out <- vars_select_eval(vars, expr, strict = strict, \n .     data = x, name_spec = name_spec, uniquely_named = uniquely_named, \n .     allow_rename = allow_rename, allow_empty = allow_empty, allow_predicates = allow_predicates, \n .     type = type, error_call = error_call), type = type)", "8. try_fetch(expr, vctrs_error_subscript = function(cnd) {\n .     cnd$subscript_action <- subscript_action(type)\n .     cnd$subscript_elt <- \"column\"\n .     cnd_signal(cnd)\n . })", "9. withCallingHandlers(expr, condition = function(cnd) {\n .     {\n .         .__handler_frame__. <- TRUE\n .         .__setup_frame__. <- frame\n .         if (inherits(cnd, \"message\")) {\n .             except <- c(\"warning\", \"error\")\n .         }\n .         else if (inherits(cnd, \"warning\")) {\n .             except <- \"error\"\n .         }\n .         else {\n .             except <- \"\"\n .         }\n .     }\n .     while (!is_null(cnd)) {\n .         if (inherits(cnd, \"vctrs_error_subscript\")) {\n .             out <- handlers[[1L]](cnd)\n .             if (!inherits(out, \"rlang_zap\")) \n .                 throw(out)\n .         }\n .         inherit <- .subset2(.subset2(cnd, \"rlang\"), \"inherit\")\n .         if (is_false(inherit)) {\n .             return()\n .         }\n .         cnd <- .subset2(cnd, \"parent\")\n .     }\n . })", "10. vars_select_eval(vars, expr, strict = strict, data = x, name_spec = name_spec, \n  .     uniquely_named = uniquely_named, allow_rename = allow_rename, \n  .     allow_empty = allow_empty, allow_predicates = allow_predicates, \n  .     type = type, error_call = error_call)", "11. walk_data_tree(expr, data_mask, context_mask)", "12. eval_c(expr, data_mask, context_mask)", "13. reduce_sels(node, data_mask, context_mask, init = init)", "14. walk_data_tree(new, data_mask, context_mask)", "15. as_indices_sel_impl(out, vars = vars, strict = strict, data = data, \n  .     allow_predicates = allow_predicates, call = error_call, arg = as_label(expr))", "16. as_indices_impl(x, vars, call = call, arg = arg, strict = strict)", "17. chr_as_locations(x, vars, call = call, arg = arg)", "18. vctrs::vec_as_location(x, n = length(vars), names = vars, call = call, \n  .     arg = arg)", "19. (function () \n  . stop_subscript_oob(i = i, subscript_type = subscript_type, names = names, \n  .     subscript_action = subscript_action, subscript_arg = subscript_arg, \n  .     call = call))()", "20. stop_subscript_oob(i = i, subscript_type = subscript_type, names = names, \n  .     subscript_action = subscript_action, subscript_arg = subscript_arg, \n  .     call = call)", "21. stop_subscript(class = \"vctrs_error_subscript_oob\", i = i, subscript_type = subscript_type, \n  .     ..., call = call)", "22. abort(class = c(class, \"vctrs_error_subscript\"), i = i, ..., \n  .     call = call)", "23. signal_abort(cnd, .file)", "24. signalCondition(cnd)", "25. (function (cnd) \n  . {\n  .     {\n  .         .__handler_frame__. <- TRUE\n  .         .__setup_frame__. <- frame\n  .         if (inherits(cnd, \"message\")) {\n  .             except <- c(\"warning\", \"error\")\n  .         }\n  .         else if (inherits(cnd, \"warning\")) {\n  .             except <- \"error\"\n  .         }\n  .         else {\n  .             except <- \"\"\n  .         }\n  .     }\n  .     while (!is_null(cnd)) {\n  .         if (inherits(cnd, \"vctrs_error_subscript\")) {\n  .             out <- handlers[[1L]](cnd)\n  .             if (!inherits(out, \"rlang_zap\")) \n  .                 throw(out)\n  .         }\n  .         inherit <- .subset2(.subset2(cnd, \"rlang\"), \"inherit\")\n  .         if (is_false(inherit)) {\n  .             return()\n  .         }\n  .         cnd <- .subset2(cnd, \"parent\")\n  .     }\n  . })(structure(list(message = \"\", trace = structure(list(call = list(\n  .     IRkernel::main(), kernel$run(), handle_shell(), executor$execute(msg), \n  .     tryCatch(evaluate(request$content$code, envir = .GlobalEnv, \n  .         output_handler = oh, stop_on_error = 1L), interrupt = function(cond) {\n  .         log_debug(\"Interrupt during execution\")\n  .         interrupted <<- TRUE\n  .     }, error = .self$handle_error), tryCatchList(expr, classes, \n  .         parentenv, handlers), tryCatchOne(tryCatchList(expr, \n  .         names[-nh], parentenv, handlers[-nh]), names[nh], parentenv, \n  .         handlers[[nh]]), doTryCatch(return(expr), name, parentenv, \n  .         handler), tryCatchList(expr, names[-nh], parentenv, handlers[-nh]), \n  .     tryCatchOne(expr, names, parentenv, handlers[[1L]]), doTryCatch(return(expr), \n  .         name, parentenv, handler), evaluate(request$content$code, \n  .         envir = .GlobalEnv, output_handler = oh, stop_on_error = 1L), \n  .     evaluate_call(expr, parsed$src[[i]], envir = envir, enclos = enclos, \n  .         debug = debug, last = i == length(out), use_try = stop_on_error != \n  .             2L, keep_warning = keep_warning, keep_message = keep_message, \n  .         log_echo = log_echo, log_warning = log_warning, output_handler = output_handler, \n  .         include_timing = include_timing), timing_fn(handle(ev <- withCallingHandlers(withVisible(eval_with_user_handlers(expr, \n  .         envir, enclos, user_handlers)), warning = wHandler, error = eHandler, \n  .         message = mHandler))), handle(ev <- withCallingHandlers(withVisible(eval_with_user_handlers(expr, \n  .         envir, enclos, user_handlers)), warning = wHandler, error = eHandler, \n  .         message = mHandler)), try(f, silent = TRUE), tryCatch(expr, \n  .         error = function(e) {\n  .             call <- conditionCall(e)\n  .             if (!is.null(call)) {\n  .                 if (identical(call[[1L]], quote(doTryCatch))) \n  .                   call <- sys.call(-4L)\n  .                 dcall <- deparse(call, nlines = 1L)\n  .                 prefix <- paste(\"Error in\", dcall, \": \")\n  .                 LONG <- 75L\n  .                 sm <- strsplit(conditionMessage(e), \"\\n\")[[1L]]\n  .                 w <- 14L + nchar(dcall, type = \"w\") + nchar(sm[1L], \n  .                   type = \"w\")\n  .                 if (is.na(w)) \n  .                   w <- 14L + nchar(dcall, type = \"b\") + nchar(sm[1L], \n  .                     type = \"b\")\n  .                 if (w > LONG) \n  .                   prefix <- paste0(prefix, \"\\n  \")\n  .             }\n  .             else prefix <- \"Error : \"\n  .             msg <- paste0(prefix, conditionMessage(e), \"\\n\")\n  .             .Internal(seterrmessage(msg[1L]))\n  .             if (!silent && isTRUE(getOption(\"show.error.messages\"))) {\n  .                 cat(msg, file = outFile)\n  .                 .Internal(printDeferredWarnings())\n  .             }\n  .             invisible(structure(msg, class = \"try-error\", condition = e))\n  .         }), tryCatchList(expr, classes, parentenv, handlers), \n  .     tryCatchOne(expr, names, parentenv, handlers[[1L]]), doTryCatch(return(expr), \n  .         name, parentenv, handler), withCallingHandlers(withVisible(eval_with_user_handlers(expr, \n  .         envir, enclos, user_handlers)), warning = wHandler, error = eHandler, \n  .         message = mHandler), withVisible(eval_with_user_handlers(expr, \n  .         envir, enclos, user_handlers)), eval_with_user_handlers(expr, \n  .         envir, enclos, user_handlers), eval(expr, envir, enclos), \n  .     eval(expr, envir, enclos), compiled_data_test %>% group_by(cohort_id) %>% \n  .         complete(relativized_time = seq(min(relativized_time), \n  .             max(relativized_time), by = 1)) %>% fill(log_pr_throughput, \n  .         is_treated, log_tenure, log_commit_percent, log_commits, \n  .         .direction = \"down\") %>% ungroup(), ungroup(.), fill(., \n  .         log_pr_throughput, is_treated, log_tenure, log_commit_percent, \n  .         log_commits, .direction = \"down\"), fill.data.frame(., \n  .         log_pr_throughput, is_treated, log_tenure, log_commit_percent, \n  .         log_commits, .direction = \"down\"), tidyselect::eval_select(expr(c(...)), \n  .         data, allow_rename = FALSE), eval_select_impl(data, names(data), \n  .         as_quosure(expr, env), include = include, exclude = exclude, \n  .         strict = strict, name_spec = name_spec, allow_rename = allow_rename, \n  .         allow_empty = allow_empty, allow_predicates = allow_predicates, \n  .         error_call = error_call, ), with_subscript_errors(out <- vars_select_eval(vars, \n  .         expr, strict = strict, data = x, name_spec = name_spec, \n  .         uniquely_named = uniquely_named, allow_rename = allow_rename, \n  .         allow_empty = allow_empty, allow_predicates = allow_predicates, \n  .         type = type, error_call = error_call), type = type), \n  .     try_fetch(expr, vctrs_error_subscript = function(cnd) {\n  .         cnd$subscript_action <- subscript_action(type)\n  .         cnd$subscript_elt <- \"column\"\n  .         cnd_signal(cnd)\n  .     }), withCallingHandlers(expr, condition = function(cnd) {\n  .         {\n  .             .__handler_frame__. <- TRUE\n  .             .__setup_frame__. <- frame\n  .             if (inherits(cnd, \"message\")) {\n  .                 except <- c(\"warning\", \"error\")\n  .             }\n  .             else if (inherits(cnd, \"warning\")) {\n  .                 except <- \"error\"\n  .             }\n  .             else {\n  .                 except <- \"\"\n  .             }\n  .         }\n  .         while (!is_null(cnd)) {\n  .             if (inherits(cnd, \"vctrs_error_subscript\")) {\n  .                 out <- handlers[[1L]](cnd)\n  .                 if (!inherits(out, \"rlang_zap\")) \n  .                   throw(out)\n  .             }\n  .             inherit <- .subset2(.subset2(cnd, \"rlang\"), \"inherit\")\n  .             if (is_false(inherit)) {\n  .                 return()\n  .             }\n  .             cnd <- .subset2(cnd, \"parent\")\n  .         }\n  .     }), vars_select_eval(vars, expr, strict = strict, data = x, \n  .         name_spec = name_spec, uniquely_named = uniquely_named, \n  .         allow_rename = allow_rename, allow_empty = allow_empty, \n  .         allow_predicates = allow_predicates, type = type, error_call = error_call), \n  .     walk_data_tree(expr, data_mask, context_mask), eval_c(expr, \n  .         data_mask, context_mask), reduce_sels(node, data_mask, \n  .         context_mask, init = init), walk_data_tree(new, data_mask, \n  .         context_mask), as_indices_sel_impl(out, vars = vars, \n  .         strict = strict, data = data, allow_predicates = allow_predicates, \n  .         call = error_call, arg = as_label(expr)), as_indices_impl(x, \n  .         vars, call = call, arg = arg, strict = strict), chr_as_locations(x, \n  .         vars, call = call, arg = arg), vctrs::vec_as_location(x, \n  .         n = length(vars), names = vars, call = call, arg = arg), \n  .     `<fn>`(), stop_subscript_oob(i = i, subscript_type = subscript_type, \n  .         names = names, subscript_action = subscript_action, subscript_arg = subscript_arg, \n  .         call = call), stop_subscript(class = \"vctrs_error_subscript_oob\", \n  .         i = i, subscript_type = subscript_type, ..., call = call), \n  .     abort(class = c(class, \"vctrs_error_subscript\"), i = i, ..., \n  .         call = call)), parent = c(0L, 1L, 2L, 3L, 4L, 5L, 6L, \n  . 7L, 6L, 9L, 10L, 4L, 12L, 13L, 13L, 15L, 16L, 17L, 18L, 19L, \n  . 13L, 13L, 13L, 23L, 24L, 0L, 0L, 0L, 0L, 29L, 30L, 31L, 32L, \n  . 33L, 31L, 35L, 36L, 37L, 38L, 39L, 40L, 41L, 42L, 0L, 44L, 45L, \n  . 46L), visible = c(TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, \n  . TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, \n  . TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE), namespace = c(\"IRkernel\", \n  . NA, \"IRkernel\", NA, \"base\", \"base\", \"base\", \"base\", \"base\", \"base\", \n  . \"base\", \"evaluate\", \"evaluate\", \"evaluate\", \"evaluate\", \"base\", \n  . \"base\", \"base\", \"base\", \"base\", \"base\", \"base\", \"evaluate\", \"base\", \n  . \"base\", NA, \"dplyr\", \"tidyr\", \"tidyr\", \"tidyselect\", \"tidyselect\", \n  . \"tidyselect\", \"rlang\", \"base\", \"tidyselect\", \"tidyselect\", \"tidyselect\", \n  . \"tidyselect\", \"tidyselect\", \"tidyselect\", \"tidyselect\", \"tidyselect\", \n  . \"vctrs\", \"vctrs\", \"vctrs\", \"vctrs\", \"rlang\"), scope = c(\"::\", \n  . NA, \"local\", NA, \"::\", \"local\", \"local\", \"local\", \"local\", \"local\", \n  . \"local\", \"::\", \":::\", \"local\", \"local\", \"::\", \"::\", \"local\", \n  . \"local\", \"local\", \"::\", \"::\", \":::\", \"::\", \"::\", NA, \"::\", \"::\", \n  . \":::\", \"::\", \":::\", \":::\", \"::\", \"::\", \":::\", \":::\", \":::\", \":::\", \n  . \":::\", \":::\", \":::\", \":::\", \"::\", \"local\", \":::\", \":::\", \"::\"\n  . ), error_frame = c(FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, FALSE, \n  . FALSE, FALSE, FALSE, FALSE, FALSE)), row.names = c(NA, -47L), version = 2L, class = c(\"rlang_trace\", \n  . \"rlib_trace\", \"tbl\", \"data.frame\")), parent = NULL, i = \"log_pr_throughput\", \n  .     subscript_type = \"character\", names = c(\"cohort_id\", \"relativized_time\", \n  .     \"repo_name\", \"standardized_time_weeks\", \"pr_throughput\", \n  .     \"pr_throughput_first\", \"pr_throughput_last\", \"rolling_slope\", \n  .     \"rolling_mean\", \"rolling_rate_of_change\", \"feature_sigmod_add\", \n  .     \"feature_sigmod_multiply\", \"someone_left\", \"tenure\", \"commit_percent\", \n  .     \"commits\", \"burst\", \"attrition_count\", \"mainLanguage\", \"createdAt_standardized\", \n  .     \"duration\", \"is_treated\", \"post_treatment\", \"is_post_treatment\", \n  .     \"is_treated_post_treatment\", \"project_commits\", \"project_contributors\", \n  .     \"project_age\"), subscript_action = NULL, subscript_arg = \"log_pr_throughput\", \n  .     rlang = list(inherit = TRUE), call = fill(., log_pr_throughput, \n  .         is_treated, log_tenure, log_commit_percent, log_commits, \n  .         .direction = \"down\")), class = c(\"vctrs_error_subscript_oob\", \n  . \"vctrs_error_subscript\", \"rlang_error\", \"error\", \"condition\")))", "26. handlers[[1L]](cnd)", "27. cnd_signal(cnd)", "28. signal_abort(cnd)"]}], "source": ["library(tidyr)\n", "\n", "compiled_data_test <- compiled_data_test %>%\n", "  group_by(cohort_id) %>%\n", "  complete(relativized_time = seq(min(relativized_time), max(relativized_time), by = 1)) %>%\n", "  fill(log_pr_throughput, is_treated, log_tenure, log_commit_percent, log_commits, .direction = \"down\") %>%\n", "  ungroup()\n", "\n", "\n", "# Estimate the event study model with control variables\n", "estimates_with_controls <- EventStudy(\n", "  estimator = \"OLS\",\n", "  data = compiled_data_test,\n", "  outcomevar = \"log_pr_throughput\",\n", "  policyvar = \"is_treated\",\n", "  idvar = \"cohort_id\",\n", "  timevar = \"relativized_time\",\n", "  controls = c(\"log_project_commits\", \"log_project_contributors\", \"log_project_age\"),\n", "  pre =0, post = 4,\n", "  normalize = -1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Warning message in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", :\n", "“Dataset is unbalanced.”\n", "Warning message in EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", :\n", "“Note: gaps of more than one unit in the time variable 'relativized_time' were detected. Treating these as gaps in the panel dimension.”\n"]}, {"ename": "ERROR", "evalue": "Error in vecseq(f__, len__, if (allow.cartesian || notjoin || !anyDuplicated(f__, : Join results in 3500768 rows; more than 1753242 = nrow(x)+nrow(i). Check for duplicate key values in i each of which join to the same group in x over and over again. If that's ok, try by=.EACHI to run j for each group to avoid the large allocation. If you are sure you wish to proceed, rerun with allow.cartesian=TRUE. Otherwise, please search for this error message in the FAQ, Wiki, Stack Overflow and data.table issue tracker for advice.\n", "output_type": "error", "traceback": ["Error in vecseq(f__, len__, if (allow.cartesian || notjoin || !anyDuplicated(f__, : Join results in 3500768 rows; more than 1753242 = nrow(x)+nrow(i). Check for duplicate key values in i each of which join to the same group in x over and over again. If that's ok, try by=.EACHI to run j for each group to avoid the large allocation. If you are sure you wish to proceed, rerun with allow.cartesian=TRUE. Otherwise, please search for this error message in the FAQ, Wiki, Stack Overflow and data.table issue tracker for advice.\nTraceback:\n", "1. EventStudy(estimator = \"OLS\", data = compiled_data_test, outcomevar = \"log_pr_throughput\", \n .     policyvar = \"is_treated\", idvar = \"cohort_id\", timevar = \"relativized_time\", \n .     controls = c(\"log_project_commits\", \"log_project_contributors\", \n .         \"log_project_age\"), pre = 0, post = 4, normalize = -1)", "2. ComputeShifts(data, idvar, timevar, shiftvar = policyvar, shiftvalues = c(-num_fd_leads, \n .     furthest_lag_period), timevar_holes = timevar_holes)", "3. merge(df, df_all, by = c(idvar, timevar, shiftvar), all.x = TRUE)", "4. merge.data.table(df, df_all, by = c(idvar, timevar, shiftvar), \n .     all.x = TRUE)", "5. y[x, nomatch = if (all.x) NA else NULL, on = by, allow.cartesian = allow.cartesian]", "6. `[.data.table`(y, x, nomatch = if (all.x) NA else NULL, on = by, \n .     allow.cartesian = allow.cartesian)", "7. vecseq(f__, len__, if (allow.cartesian || notjoin || !anyDuplicated(f__, \n .     incomparables = c(0L, NA_integer_))) {\n .     NULL\n . } else as.double(nrow(x) + nrow(i)))"]}], "source": ["# Estimate the event study model with control variables\n", "estimates_with_controls <- EventStudy(\n", "  estimator = \"OLS\",\n", "  data = compiled_data_test,\n", "  outcomevar = \"log_pr_throughput\",\n", "  policyvar = \"is_treated\",\n", "  idvar = \"cohort_id\",\n", "  timevar = \"relativized_time\",\n", "  controls = c(\"log_project_commits\", \"log_project_contributors\", \"log_project_age\"),\n", "  pre =0, post = 4,\n", "  normalize = -1\n", ")\n", "\n", "# Create event study plot without control variables\n", "plt_without_controls <- EventStudyPlot(estimates = estimates_without_controls)\n", "print(plt_without_controls)\n", "\n", "# Create event study plot with control variables\n", "plt_with_controls <- EventStudyPlot(estimates = estimates_with_controls)\n", "print(plt_with_controls)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"ename": "ERROR", "evalue": "\u001b[1m\u001b[33mError\u001b[39m in `mutate()`:\u001b[22m\n\u001b[1m\u001b[22m\u001b[36mℹ\u001b[39m In argument: `log_project_commits_before_treatment =\n  log(project_commits_before_treatment + 1)`.\n\u001b[1mCaused by error:\u001b[22m\n\u001b[33m!\u001b[39m object 'project_commits_before_treatment' not found\n", "output_type": "error", "traceback": ["\u001b[1m\u001b[33mError\u001b[39m in `mutate()`:\u001b[22m\n\u001b[1m\u001b[22m\u001b[36mℹ\u001b[39m In argument: `log_project_commits_before_treatment =\n  log(project_commits_before_treatment + 1)`.\n\u001b[1mCaused by error:\u001b[22m\n\u001b[33m!\u001b[39m object 'project_commits_before_treatment' not found\nTraceback:\n", "1. compiled_data_test %>% mutate(log_pr_throughput = log(pr_throughput + \n .     1), log_project_commits = log(project_commits + 1), log_project_contributors = log(project_contributors + \n .     1), log_project_age = log(project_age + 1), log_project_commits_before_treatment = log(project_commits_before_treatment + \n .     1), log_project_contributors_before_treatment = log(project_contributors_before_treatment + \n .     1), log_project_age_before_treatment = log(project_age_before_treatment + \n .     1), log_tenure = log(tenure + 1), log_commit_percent = log(commit_percent + \n .     1), log_commits = log(commits + 1))", "2. mutate(., log_pr_throughput = log(pr_throughput + 1), log_project_commits = log(project_commits + \n .     1), log_project_contributors = log(project_contributors + \n .     1), log_project_age = log(project_age + 1), log_project_commits_before_treatment = log(project_commits_before_treatment + \n .     1), log_project_contributors_before_treatment = log(project_contributors_before_treatment + \n .     1), log_project_age_before_treatment = log(project_age_before_treatment + \n .     1), log_tenure = log(tenure + 1), log_commit_percent = log(commit_percent + \n .     1), log_commits = log(commits + 1))", "3. mutate.data.frame(., log_pr_throughput = log(pr_throughput + \n .     1), log_project_commits = log(project_commits + 1), log_project_contributors = log(project_contributors + \n .     1), log_project_age = log(project_age + 1), log_project_commits_before_treatment = log(project_commits_before_treatment + \n .     1), log_project_contributors_before_treatment = log(project_contributors_before_treatment + \n .     1), log_project_age_before_treatment = log(project_age_before_treatment + \n .     1), log_tenure = log(tenure + 1), log_commit_percent = log(commit_percent + \n .     1), log_commits = log(commits + 1))", "4. mutate_cols(.data, dplyr_quosures(...), by)", "5. withCallingHandlers(for (i in seq_along(dots)) {\n .     poke_error_context(dots, i, mask = mask)\n .     context_poke(\"column\", old_current_column)\n .     new_columns <- mutate_col(dots[[i]], data, mask, new_columns)\n . }, error = dplyr_error_handler(dots = dots, mask = mask, bullets = mutate_bullets, \n .     error_call = error_call, error_class = \"dplyr:::mutate_error\"), \n .     warning = dplyr_warning_handler(state = warnings_state, mask = mask, \n .         error_call = error_call))", "6. mutate_col(dots[[i]], data, mask, new_columns)", "7. mask$eval_all_mutate(quo)", "8. eval()", "9. .handleSimpleError(function (cnd) \n . {\n .     local_error_context(dots, i = frame[[i_sym]], mask = mask)\n .     if (inherits(cnd, \"dplyr:::internal_error\")) {\n .         parent <- error_cnd(message = bullets(cnd))\n .     }\n .     else {\n .         parent <- cnd\n .     }\n .     message <- c(cnd_bullet_header(action), i = if (has_active_group_context(mask)) cnd_bullet_cur_group_label())\n .     abort(message, class = error_class, parent = parent, call = error_call)\n . }, \"object 'project_commits_before_treatment' not found\", base::quote(NULL))", "10. h(simpleError(msg, call))", "11. abort(message, class = error_class, parent = parent, call = error_call)", "12. signal_abort(cnd, .file)"]}], "source": ["# Load required libraries\n", "library(dplyr)\n", "library(lme4)\n", "library(car)\n", "library(MuMIn)\n", "\n", "# Read data\n", "\n", "# Log transformations\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    log_pr_throughput = log(pr_throughput + 1),\n", "    log_project_commits = log(project_commits + 1),\n", "    log_project_contributors = log(project_contributors + 1),\n", "    log_project_age = log(project_age + 1),\n", "    log_project_commits_before_treatment = log(project_commits_before_treatment + 1),\n", "    log_project_contributors_before_treatment = log(project_contributors_before_treatment + 1),\n", "    log_project_age_before_treatment = log(project_age_before_treatment + 1),\n", "    log_tenure = log(tenure + 1),\n", "    log_commit_percent = log(commit_percent + 1),\n", "    log_commits = log(commits + 1)\n", "  )\n", "\n", "# Create interaction variables and fixed effects\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    time_cohort_effect = paste0(is_post_treatment, \"_\", cohort_id),\n", "    repo_cohort_effect = paste0(is_treated, \"_\", cohort_id)\n", "  ) %>%\n", "  mutate(\n", "    time_cohort_effect = as.factor(time_cohort_effect),\n", "    repo_cohort_effect = as.factor(repo_cohort_effect)\n", "  )\n", "\n", "# Model 1: Fixed Effects Only\n", "model_fixed_effects_only <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_only <- vif(model_fixed_effects_only)\n", "print(vif_model_fixed_effects_only)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_only)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_only)\n", "\n", "# Model 2: Fixed Effects + Developer Characteristics\n", "model_fixed_effects_developer <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "                     log_tenure + log_commit_percent + log_commits +\n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_developer <- vif(model_fixed_effects_developer)\n", "print(vif_model_fixed_effects_developer)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_developer)\n", "\n", "# Calculate R-squared values\n", "<PERSON>.<PERSON>(model_fixed_effects_developer)\n", "\n", "# Model 3: Fixed Effects + Project Characteristics\n", "model_fixed_effects_project <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "                     log_project_commits_before_treatment + log_project_contributors_before_treatment +\n", "                     log_project_age_before_treatment + project_main_language +\n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_project <- vif(model_fixed_effects_project)\n", "print(vif_model_fixed_effects_project)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_project)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_project)\n", "\n", "# Model 4: Fixed Effects + Developer Characteristics + Project Characteristics\n", "model_fixed_effects_developer_project <- lmer(\n", "  log_pr_throughput ~ is_post_treatment + is_treated + is_treated:is_post_treatment +\n", "                     log_tenure + log_commit_percent + log_commits +\n", "                     log_project_commits_before_treatment + log_project_contributors_before_treatment +\n", "                     log_project_age_before_treatment + project_main_language +\n", "                     (1 | time_cohort_effect) + (1 | repo_cohort_effect),\n", "  REML = FALSE,\n", "  data = compiled_data_test\n", ")\n", "\n", "# Calculate VIF\n", "vif_model_fixed_effects_developer_project <- vif(model_fixed_effects_developer_project)\n", "print(vif_model_fixed_effects_developer_project)\n", "\n", "# Summary of the model\n", "summary(model_fixed_effects_developer_project)\n", "\n", "# Calculate R-squared values\n", "r.squared<PERSON>(model_fixed_effects_developer_project)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The variables 'is_treated' and 'is_post_treatment' have been removed because of collinearity (see $collin.var).\n", "\n"]}, {"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 38,409\n", "Fixed-effects: time_cohort_effect: 1,554,  repo_cohort_effect: 1,554\n", "Standard-errors: Clustered (time_cohort_effect) \n", "                              Estimate Std. Error  t value  Pr(>|t|)    \n", "is_treated:is_post_treatment -0.123477   0.011254 -10.9722 < 2.2e-16 ***\n", "... 2 variables were removed because of collinearity (is_treated and is_post_treatment)\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.499611     Adj. R2: 0.565329\n", "                 Within R2: 0.003714"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load required libraries\n", "library(fixest)  # Efficient regression with fixed effects\n", "library(dplyr)   # Data manipulation\n", "\n", "# Read data\n", "# compiled_data_test <- read.csv(\"../result/did_result_20250212/compiled_data_test.csv\")\n", "compiled_data_test <- read.csv(\"../result/standardized_productivity_20250202/compiled_data_test_with_features.csv\")\n", "# Log transformations\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    log_pr_throughput = log(pr_throughput + 1),\n", "    log_project_commits = log(project_commits + 1),\n", "    log_project_contributors = log(project_contributors + 1),\n", "    log_project_age = log(project_age + 1)\n", "  )\n", "\n", "# Create time-cohort and repo-cohort effects\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    time_cohort_effect = paste0(is_post_treatment, \"_\", cohort_id),\n", "    repo_cohort_effect = paste0(is_treated, \"_\", cohort_id)\n", "  ) %>%\n", "  mutate(\n", "    time_cohort_effect = as.factor(time_cohort_effect),\n", "    repo_cohort_effect = as.factor(repo_cohort_effect)\n", "  )\n", "\n", "# Run OLS regression with fixed effects\n", "model <- feols(\n", "  log_pr_throughput ~ is_treated * is_post_treatment | time_cohort_effect + repo_cohort_effect,\n", "  data = compiled_data_test,\n", "  # cluster = ~cohort_id  # Optional: Cluster standard errors by cohort_id\n", ")\n", "\n", "# View results\n", "summary(model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 40,449\n", "Fixed-effects: cohort_id: 818\n", "Standard-errors: Clustered (cohort_id) \n", "                 Estimate Std. Error t value  Pr(>|t|)    \n", "interaction_term -0.12196   0.011637  -10.48 < 2.2e-16 ***\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.524173     Adj. R2: 0.540369\n", "                 Within R2: 0.00967 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load required libraries\n", "library(fixest)  # Efficient regression with fixed effects\n", "library(dplyr)   # Data manipulation\n", "\n", "# Read data\n", "compiled_data_test <- read.csv(\"../result/standardized_productivity_20250202/compiled_data_test_with_features.csv\")\n", "\n", "# Log transformations\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    log_pr_throughput = log(pr_throughput + 1),\n", "    log_project_commits = log(project_commits + 1),\n", "    log_project_contributors = log(project_contributors + 1),\n", "    log_project_age = log(project_age + 1)\n", "  )\n", "\n", "# Create interaction term manually\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(interaction_term = is_treated * is_post_treatment)\n", "\n", "# Run OLS regression with fixed effects\n", "model <- feols(\n", "  log_pr_throughput ~ interaction_term | cohort_id,\n", "  data = compiled_data_test,\n", "  cluster = ~cohort_id\n", ")\n", "\n", "# View results\n", "summary(model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"data": {"text/plain": ["\n", "    0     1 \n", "20196 20253 "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["\n", "    0     1 \n", "21269 19180 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 检查变量的分布\n", "table(compiled_data_test$is_treated)\n", "table(compiled_data_test$is_post_treatment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The variables 'is_treated' and 'is_post_treatment' have been removed because of collinearity (see $collin.var).\n", "\n"]}, {"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 40,449\n", "Fixed-effects: time_cohort_effect: 1,636,  repo_cohort_effect: 1,636\n", "Standard-errors: Clustered (cohort_id) \n", "                              Estimate Std. Error  t value   Pr(>|t|)    \n", "is_treated:is_post_treatment -0.120306   0.015131 -7.95074 6.1564e-15 ***\n", "... 2 variables were removed because of collinearity (is_treated and is_post_treatment)\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.497721     Adj. R2: 0.558245\n", "                 Within R2: 0.003557"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load required libraries\n", "library(fixest)  # Efficient regression with fixed effects\n", "library(dplyr)   # Data manipulation\n", "\n", "# Read data\n", "# compiled_data_test <- read.csv(\"../result/did_result_20250212/compiled_data_test.csv\")\n", "compiled_data_test <- read.csv(\"../result/standardized_productivity_20250202/compiled_data_test_with_features.csv\")\n", "# compiled_data_test <- read.csv(\"../result/standardized_productivity_20250202/compiled_data_test.csv\")\n", "# Log transformations\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    log_pr_throughput = log(pr_throughput + 1),\n", "    log_project_commits = log(project_commits + 1),\n", "    log_project_contributors = log(project_contributors + 1),\n", "    log_project_age = log(project_age + 1)\n", "  )\n", "\n", "# Create time-cohort and repo-cohort effects\n", "compiled_data_test <- compiled_data_test %>%\n", "  mutate(\n", "    time_cohort_effect = paste0(is_post_treatment, \"_\", cohort_id),\n", "    repo_cohort_effect = paste0(is_treated, \"_\", cohort_id)\n", "  ) %>%\n", "  mutate(\n", "    time_cohort_effect = as.factor(time_cohort_effect),\n", "    repo_cohort_effect = as.factor(repo_cohort_effect)\n", "  )\n", "\n", "# Run OLS regression with fixed effects\n", "model <- feols(\n", "  log_pr_throughput ~ is_treated * is_post_treatment | time_cohort_effect + repo_cohort_effect,\n", "  data = compiled_data_test,\n", "  cluster = ~cohort_id  # Optional: Cluster standard errors by cohort_id\n", ")\n", "\n", "# View results\n", "summary(model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The variables 'is_treated', 'is_post_treatment', 'log_tenure', 'log_commit_percent', 'log_commits', 'is_treated:log_tenure' and 5 others have been removed because of collinearity (see $collin.var).\n", "\n"]}, {"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 40,449\n", "Fixed-effects: time_cohort_effect: 1,636,  repo_cohort_effect: 1,636\n", "Standard-errors: Clustered (cohort_id) \n", "                                                 Estimate Std. Error   t value\n", "is_treated:is_post_treatment                    -0.196069   0.075892 -2.583538\n", "is_treated:is_post_treatment:log_tenure         -0.002177   0.013047 -0.166869\n", "is_treated:is_post_treatment:log_commit_percent -0.156620   0.090961 -1.721835\n", "is_treated:is_post_treatment:log_commits         0.026767   0.013305  2.011817\n", "                                                 Pr(>|t|)    \n", "is_treated:is_post_treatment                    0.0099519 ** \n", "is_treated:is_post_treatment:log_tenure         0.8675143    \n", "is_treated:is_post_treatment:log_commit_percent 0.0854779 .  \n", "is_treated:is_post_treatment:log_commits        0.0445670 *  \n", "... 11 variables were removed because of collinearity (is_treated, is_post_treatment and 9 others [full set in $collin.var])\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.497654     Adj. R2: 0.558328\n", "                 Within R2: 0.003825"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_with_features_left_core_dev <- feols(\n", "  log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits) | time_cohort_effect + repo_cohort_effect,\n", "  data = compiled_data_test,\n", "  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)\n", ")\n", "\n", "summary(model_with_features_left_core_dev)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The variables 'is_treated', 'is_post_treatment', 'log_project_commits_before_treatment', 'log_project_contributors_before_treatment', 'log_project_age_before_treatment', 'project_main_languageC#' and 32 others have been removed because of collinearity (see $collin.var).\n", "\n"]}, {"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 40,449\n", "Fixed-effects: time_cohort_effect: 1,636,  repo_cohort_effect: 1,636\n", "Standard-errors: Clustered (cohort_id) \n", "                                                                        Estimate\n", "is_treated:is_post_treatment                                           -0.178185\n", "is_treated:is_post_treatment:log_project_commits_before_treatment       0.008266\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment  0.017644\n", "is_treated:is_post_treatment:log_project_age_before_treatment           0.000961\n", "is_treated:is_post_treatment:project_main_languageC#                   -0.049761\n", "is_treated:is_post_treatment:project_main_languageC++                  -0.018201\n", "is_treated:is_post_treatment:project_main_languageGo                   -0.037519\n", "is_treated:is_post_treatment:project_main_languageJava                 -0.041503\n", "is_treated:is_post_treatment:project_main_languageJavaScript           -0.159474\n", "is_treated:is_post_treatment:project_main_languagePHP                  -0.123764\n", "is_treated:is_post_treatment:project_main_languagePython               -0.053437\n", "is_treated:is_post_treatment:project_main_languageRust                 -0.028460\n", "is_treated:is_post_treatment:project_main_languageTypeScript            0.025278\n", "                                                                       <PERSON>d<PERSON>\n", "is_treated:is_post_treatment                                             0.144737\n", "is_treated:is_post_treatment:log_project_commits_before_treatment        0.015229\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment   0.031407\n", "is_treated:is_post_treatment:log_project_age_before_treatment            0.021581\n", "is_treated:is_post_treatment:project_main_languageC#                     0.059536\n", "is_treated:is_post_treatment:project_main_languageC++                    0.057212\n", "is_treated:is_post_treatment:project_main_languageGo                     0.074053\n", "is_treated:is_post_treatment:project_main_languageJava                   0.061539\n", "is_treated:is_post_treatment:project_main_languageJavaScript             0.056781\n", "is_treated:is_post_treatment:project_main_languagePHP                    0.055656\n", "is_treated:is_post_treatment:project_main_languagePython                 0.059951\n", "is_treated:is_post_treatment:project_main_languageRust                   0.074545\n", "is_treated:is_post_treatment:project_main_languageTypeScript             0.074382\n", "                                                                         t value\n", "is_treated:is_post_treatment                                           -1.231088\n", "is_treated:is_post_treatment:log_project_commits_before_treatment       0.542774\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment  0.561806\n", "is_treated:is_post_treatment:log_project_age_before_treatment           0.044547\n", "is_treated:is_post_treatment:project_main_languageC#                   -0.835814\n", "is_treated:is_post_treatment:project_main_languageC++                  -0.318128\n", "is_treated:is_post_treatment:project_main_languageGo                   -0.506646\n", "is_treated:is_post_treatment:project_main_languageJava                 -0.674426\n", "is_treated:is_post_treatment:project_main_languageJavaScript           -2.808595\n", "is_treated:is_post_treatment:project_main_languagePHP                  -2.223751\n", "is_treated:is_post_treatment:project_main_languagePython               -0.891339\n", "is_treated:is_post_treatment:project_main_languageRust                 -0.381778\n", "is_treated:is_post_treatment:project_main_languageTypeScript            0.339835\n", "                                                                        Pr(>|t|)\n", "is_treated:is_post_treatment                                           0.2186441\n", "is_treated:is_post_treatment:log_project_commits_before_treatment      0.5874332\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment 0.5744025\n", "is_treated:is_post_treatment:log_project_age_before_treatment          0.9644795\n", "is_treated:is_post_treatment:project_main_languageC#                   0.4035041\n", "is_treated:is_post_treatment:project_main_languageC++                  0.7504690\n", "is_treated:is_post_treatment:project_main_languageGo                   0.6125401\n", "is_treated:is_post_treatment:project_main_languageJava                 0.5002315\n", "is_treated:is_post_treatment:project_main_languageJavaScript           0.0050945\n", "is_treated:is_post_treatment:project_main_languagePHP                  0.0264379\n", "is_treated:is_post_treatment:project_main_languagePython               0.3730099\n", "is_treated:is_post_treatment:project_main_languageRust                 0.7027250\n", "is_treated:is_post_treatment:project_main_languageTypeScript           0.7340682\n", "                                                                          \n", "is_treated:is_post_treatment                                              \n", "is_treated:is_post_treatment:log_project_commits_before_treatment         \n", "is_treated:is_post_treatment:log_project_contributors_before_treatment    \n", "is_treated:is_post_treatment:log_project_age_before_treatment             \n", "is_treated:is_post_treatment:project_main_languageC#                      \n", "is_treated:is_post_treatment:project_main_languageC++                     \n", "is_treated:is_post_treatment:project_main_languageGo                      \n", "is_treated:is_post_treatment:project_main_languageJava                    \n", "is_treated:is_post_treatment:project_main_languageJavaScript           ** \n", "is_treated:is_post_treatment:project_main_languagePHP                  *  \n", "is_treated:is_post_treatment:project_main_languagePython                  \n", "is_treated:is_post_treatment:project_main_languageRust                    \n", "is_treated:is_post_treatment:project_main_languageTypeScript              \n", "... 38 variables were removed because of collinearity (is_treated, is_post_treatment and 36 others [full set in $collin.var])\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.497497     Adj. R2: 0.5585  \n", "                 Within R2: 0.004453"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_with_features_project_characteristics <- feols(\n", "  log_pr_throughput ~ is_treated * is_post_treatment * (log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) | time_cohort_effect + repo_cohort_effect,\n", "  data = compiled_data_test,\n", "  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)\n", ")\n", "\n", "summary(model_with_features_project_characteristics)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "r"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The variables 'is_treated', 'is_post_treatment', 'log_tenure', 'log_commit_percent', 'log_commits', 'log_project_commits_before_treatment' and 41 others have been removed because of collinearity (see $collin.var).\n", "\n"]}, {"data": {"text/plain": ["OLS estimation, Dep. Var.: log_pr_throughput\n", "Observations: 40,449\n", "Fixed-effects: time_cohort_effect: 1,636,  repo_cohort_effect: 1,636\n", "Standard-errors: Clustered (cohort_id) \n", "                                                                        Estimate\n", "is_treated:is_post_treatment                                           -0.143660\n", "is_treated:is_post_treatment:log_tenure                                -0.007944\n", "is_treated:is_post_treatment:log_commit_percent                        -0.172990\n", "is_treated:is_post_treatment:log_commits                                0.042889\n", "is_treated:is_post_treatment:log_project_commits_before_treatment      -0.029597\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment  0.027891\n", "is_treated:is_post_treatment:log_project_age_before_treatment           0.009469\n", "is_treated:is_post_treatment:project_main_languageC#                   -0.055737\n", "is_treated:is_post_treatment:project_main_languageC++                  -0.020431\n", "is_treated:is_post_treatment:project_main_languageGo                   -0.038771\n", "is_treated:is_post_treatment:project_main_languageJava                 -0.040672\n", "is_treated:is_post_treatment:project_main_languageJavaScript           -0.161752\n", "is_treated:is_post_treatment:project_main_languagePHP                  -0.126823\n", "is_treated:is_post_treatment:project_main_languagePython               -0.059052\n", "is_treated:is_post_treatment:project_main_languageRust                 -0.034928\n", "is_treated:is_post_treatment:project_main_languageTypeScript            0.019230\n", "                                                                       <PERSON>d<PERSON>\n", "is_treated:is_post_treatment                                             0.142906\n", "is_treated:is_post_treatment:log_tenure                                  0.014886\n", "is_treated:is_post_treatment:log_commit_percent                          0.166225\n", "is_treated:is_post_treatment:log_commits                                 0.037082\n", "is_treated:is_post_treatment:log_project_commits_before_treatment        0.035314\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment   0.032070\n", "is_treated:is_post_treatment:log_project_age_before_treatment            0.025010\n", "is_treated:is_post_treatment:project_main_languageC#                     0.059171\n", "is_treated:is_post_treatment:project_main_languageC++                    0.057172\n", "is_treated:is_post_treatment:project_main_languageGo                     0.074024\n", "is_treated:is_post_treatment:project_main_languageJava                   0.061246\n", "is_treated:is_post_treatment:project_main_languageJavaScript             0.056753\n", "is_treated:is_post_treatment:project_main_languagePHP                    0.055123\n", "is_treated:is_post_treatment:project_main_languagePython                 0.059540\n", "is_treated:is_post_treatment:project_main_languageRust                   0.074057\n", "is_treated:is_post_treatment:project_main_languageTypeScript             0.073581\n", "                                                                         t value\n", "is_treated:is_post_treatment                                           -1.005280\n", "is_treated:is_post_treatment:log_tenure                                -0.533621\n", "is_treated:is_post_treatment:log_commit_percent                        -1.040701\n", "is_treated:is_post_treatment:log_commits                                1.156594\n", "is_treated:is_post_treatment:log_project_commits_before_treatment      -0.838120\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment  0.869685\n", "is_treated:is_post_treatment:log_project_age_before_treatment           0.378611\n", "is_treated:is_post_treatment:project_main_languageC#                   -0.941955\n", "is_treated:is_post_treatment:project_main_languageC++                  -0.357358\n", "is_treated:is_post_treatment:project_main_languageGo                   -0.523759\n", "is_treated:is_post_treatment:project_main_languageJava                 -0.664082\n", "is_treated:is_post_treatment:project_main_languageJavaScript           -2.850130\n", "is_treated:is_post_treatment:project_main_languagePHP                  -2.300743\n", "is_treated:is_post_treatment:project_main_languagePython               -0.991799\n", "is_treated:is_post_treatment:project_main_languageRust                 -0.471636\n", "is_treated:is_post_treatment:project_main_languageTypeScript            0.261349\n", "                                                                        Pr(>|t|)\n", "is_treated:is_post_treatment                                           0.3150597\n", "is_treated:is_post_treatment:log_tenure                                0.5937492\n", "is_treated:is_post_treatment:log_commit_percent                        0.2983220\n", "is_treated:is_post_treatment:log_commits                               0.2477762\n", "is_treated:is_post_treatment:log_project_commits_before_treatment      0.4022086\n", "is_treated:is_post_treatment:log_project_contributors_before_treatment 0.3847281\n", "is_treated:is_post_treatment:log_project_age_before_treatment          0.7050751\n", "is_treated:is_post_treatment:project_main_languageC#                   0.3464944\n", "is_treated:is_post_treatment:project_main_languageC++                  0.7209162\n", "is_treated:is_post_treatment:project_main_languageGo                   0.6005879\n", "is_treated:is_post_treatment:project_main_languageJava                 0.5068253\n", "is_treated:is_post_treatment:project_main_languageJavaScript           0.0044801\n", "is_treated:is_post_treatment:project_main_languagePHP                  0.0216572\n", "is_treated:is_post_treatment:project_main_languagePython               0.3215892\n", "is_treated:is_post_treatment:project_main_languageRust                 0.6373129\n", "is_treated:is_post_treatment:project_main_languageTypeScript           0.7938893\n", "                                                                          \n", "is_treated:is_post_treatment                                              \n", "is_treated:is_post_treatment:log_tenure                                   \n", "is_treated:is_post_treatment:log_commit_percent                           \n", "is_treated:is_post_treatment:log_commits                                  \n", "is_treated:is_post_treatment:log_project_commits_before_treatment         \n", "is_treated:is_post_treatment:log_project_contributors_before_treatment    \n", "is_treated:is_post_treatment:log_project_age_before_treatment             \n", "is_treated:is_post_treatment:project_main_languageC#                      \n", "is_treated:is_post_treatment:project_main_languageC++                     \n", "is_treated:is_post_treatment:project_main_languageGo                      \n", "is_treated:is_post_treatment:project_main_languageJava                    \n", "is_treated:is_post_treatment:project_main_languageJavaScript           ** \n", "is_treated:is_post_treatment:project_main_languagePHP                  *  \n", "is_treated:is_post_treatment:project_main_languagePython                  \n", "is_treated:is_post_treatment:project_main_languageRust                    \n", "is_treated:is_post_treatment:project_main_languageTypeScript              \n", "... 47 variables were removed because of collinearity (is_treated, is_post_treatment and 45 others [full set in $collin.var])\n", "---\n", "Signif. codes:  0 '***' 0.001 '**' 0.01 '*' 0.05 '.' 0.1 ' ' 1\n", "RMSE: 0.497471     Adj. R2: 0.558509\n", "                 Within R2: 0.004555"]}, "metadata": {}, "output_type": "display_data"}], "source": ["model_with_features_left_core_dev_and_project_characteristics <- feols(\n", "  log_pr_throughput ~ is_treated * is_post_treatment * (log_tenure + log_commit_percent + log_commits + log_project_commits_before_treatment + log_project_contributors_before_treatment + log_project_age_before_treatment + project_main_language) | time_cohort_effect + repo_cohort_effect,\n", "  data = compiled_data_test,\n", "  cluster = ~cohort_id  # Cluster standard errors by cohort_id (optional)\n", ")\n", "\n", "summary(model_with_features_left_core_dev_and_project_characteristics)"]}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.3.1"}}, "nbformat": 4, "nbformat_minor": 2}