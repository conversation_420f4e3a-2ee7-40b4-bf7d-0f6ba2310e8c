import pandas as pd
import os
import logging
import numpy as np
from sklearn.neighbors import NearestNeighbors
import gc
import psutil
import pickle
from typing import Dict, List, Tuple, Generator
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
from datetime import datetime
import sys

# Set multiprocessing start method to spawn
multiprocessing.set_start_method('spawn', force=True)

# Define limits to process
# ATTRITION_LIMITS = [365, 180, 270, 450]
# ATTRITION_LIMITS = [180]
ATTRITION_LIMITS = [365]

# Get number of CPU cores
N_CPUS = multiprocessing.cpu_count()
print(f"Available CPU cores: {N_CPUS}")

class MemoryTracker:
    """内存使用跟踪器"""
    def __init__(self, logger=None):
        self.logger = logger
        self.process = psutil.Process()
        self.peak_memory = 0
        
    def get_memory_mb(self):
        """获取当前内存使用量(MB)"""
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        self.peak_memory = max(self.peak_memory, memory_mb)
        return memory_mb
    
    def log_memory(self, stage_name=""):
        """记录内存使用情况"""
        current_memory = self.get_memory_mb()
        if self.logger:
            self.logger.info(f"[MEMORY] {stage_name}: {current_memory:.1f}MB (Peak: {self.peak_memory:.1f}MB)")
        return current_memory

class TimeTracker:
    """时间跟踪器"""
    def __init__(self, logger=None):
        self.logger = logger
        self.start_times = {}
        self.durations = {}
        
    def start(self, stage_name):
        """开始计时"""
        self.start_times[stage_name] = time.time()
        if self.logger:
            self.logger.info(f"[TIME] Starting {stage_name} at {datetime.now().strftime('%H:%M:%S')}")
    
    def end(self, stage_name):
        """结束计时"""
        if stage_name in self.start_times:
            duration = time.time() - self.start_times[stage_name]
            self.durations[stage_name] = duration
            if self.logger:
                self.logger.info(f"[TIME] Completed {stage_name} in {duration:.2f}s at {datetime.now().strftime('%H:%M:%S')}")
            return duration
        return 0
    
    def get_summary(self):
        """获取时间统计摘要"""
        return self.durations.copy()

def setup_logging_for_limit(limit):
    """Set up independent logging configuration for each limit"""
    log_dir = f"../logs/limit_{limit}"
    os.makedirs(log_dir, exist_ok=True)
    
    # Create independent log file
    log_file = os.path.join(log_dir, f"psm_matching_limit{limit}_improved.log")
    
    # Configure logger
    logger = logging.getLogger(f'limit_{limit}_improved')
    logger.setLevel(logging.INFO)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Add file handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(filename)s:%(lineno)d] %(levelname)s: %(message)s")
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Add console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

def memory_and_time_monitor(func):
    """监控函数执行期间的内存使用和时间的装饰器"""
    def wrapper(*args, **kwargs):
        # 获取logger参数
        logger = kwargs.get('logger') or (args[7] if len(args) > 7 else None)
        
        memory_tracker = MemoryTracker(logger)
        time_tracker = TimeTracker(logger)
        
        func_name = func.__name__
        time_tracker.start(func_name)
        memory_tracker.log_memory(f"Before {func_name}")
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            memory_tracker.log_memory(f"After {func_name}")
            time_tracker.end(func_name)
            
            # 强制垃圾回收
            gc.collect()
            memory_tracker.log_memory(f"After GC {func_name}")
            
    return wrapper

def process_in_batches(
    data: pd.DataFrame,
    batch_size: int = 1000
) -> Generator[pd.DataFrame, None, None]:
    """按批次处理数据，避免一次性加载全部数据"""
    for i in range(0, len(data), batch_size):
        yield data.iloc[i:i + batch_size]

def prepare_data(
    p_test: pd.DataFrame,
    p_test_attrition: pd.DataFrame
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    预处理数据的函数
    """
    # 使用inplace=True来减少内存使用
    p_test.fillna(0, inplace=True)
    p_test_attrition.fillna(0, inplace=True)

    # 转换数据类型以节省内存
    p_test_attrition['burst'] = p_test_attrition['burst'].astype(np.int32)

    return p_test, p_test_attrition

def compile_control_group_psm_knn(
    treatment_repos_with_left_date: pd.DataFrame,
    candidate_repos: List[str],
    productivity_metric_data: pd.DataFrame,
    n_neighbors: int,
    timewindow_weeks: int,
    feature_columns: List[str],
    extra_candidates: int = 10,
    batch_size: int = 1000,  # 添加批处理大小参数
    logger=None
) -> Tuple[Dict, pd.DataFrame, pd.DataFrame]:
    """
    优化后的PSM-KNN匹配函数，使用批处理和内存管理
    """
    if logger is None:
        logger = logging.getLogger()

    # 添加超时机制
    function_start_time = time.time()
    max_function_time = 3600  # 1小时超时

    logging.info("Starting precision PSM with NearestNeighbors...")

    # 预处理控制组数据
    treatment_bursts = set(treatment_repos_with_left_date["burst"])
    treatment_repos = set(treatment_repos_with_left_date["repo_name"])
    available_controls = set(candidate_repos)

    logging.info(f"Available controls: {len(available_controls)} repositories to match {len(treatment_repos)} treatment repositories in {len(treatment_bursts)} bursts")

    # 构建带时间标识的控制组数据
    control_data = productivity_metric_data[
        productivity_metric_data["repo_name"].isin(available_controls)
    ].copy()

    # 使用更高效的方式创建time_key
    control_data["time_key"] = list(zip(
        control_data["repo_name"],
        control_data["standardized_time_weeks"]
    ))

    # 预计算控制组离职时间点
    control_treatment_weeks = {}
    for name, group in productivity_metric_data[
        (productivity_metric_data["someone_left"] == 1) &
        (productivity_metric_data["repo_name"].isin(available_controls))
    ].groupby("repo_name")["standardized_time_weeks"]:
        control_treatment_weeks[name] = sorted(group.values)

    # 准备特征矩阵
    treatment_mask = (
        productivity_metric_data["repo_name"].isin(treatment_repos)
    ) & (productivity_metric_data["someone_left"] == 1)
    treatment_features_df = productivity_metric_data[treatment_mask].copy()

    X_control = control_data[feature_columns].values

    # 构建NearestNeighbors索引
    nn_model = NearestNeighbors(
        n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
        algorithm='auto'
    )
    nn_model.fit(X_control)

    matched_pairs = {}
    control_data_indexed = control_data.reset_index(drop=True)

    # 批量处理treatment cases
    total_treatment_cases = len(treatment_features_df)
    processed_cases = 0

    logger.info(f"Starting to process {total_treatment_cases} treatment cases in batches of {batch_size}")

    for batch_idx, batch_df in enumerate(process_in_batches(treatment_features_df, batch_size)):
        batch_start_time = time.time()
        logger.info(f"Processing batch {batch_idx + 1}, cases {processed_cases + 1} to {min(processed_cases + len(batch_df), total_treatment_cases)}")

        for _, t_row in batch_df.iterrows():
            # 检查超时
            if time.time() - function_start_time > max_function_time:
                logger.error(f"Function timeout after {max_function_time}s, stopping processing")
                break

            t_burst = t_row["burst"]
            t_repo = t_row["repo_name"]
            t_time = t_row["standardized_time_weeks"]
            used_time_keys = set()
            matched_controls = []

            neighbor_query_size = n_neighbors * extra_candidates
            t_features = t_row[feature_columns].values.reshape(1, -1)
            max_iterations = 10  # 防止无限循环
            iteration_count = 0

            while (len(matched_controls) < n_neighbors and
                   neighbor_query_size <= len(X_control) and
                   iteration_count < max_iterations):

                iteration_count += 1
                previous_matched_count = len(matched_controls)

                distances, indices = nn_model.kneighbors(
                    t_features,
                    n_neighbors=min(neighbor_query_size, len(X_control)),
                    return_distance=True
                )
                indices = indices[0]
                distances = distances[0]

                # 使用列表推导式优化
                candidate_indices = [
                    idx for idx in indices
                    if control_data_indexed.iloc[idx]["time_key"] not in used_time_keys
                ]

                for idx in candidate_indices:
                    if len(matched_controls) >= n_neighbors:
                        break

                    control_row = control_data_indexed.iloc[idx]
                    control_repo, control_time = control_row["time_key"]

                    # 时间窗口过滤
                    if not (t_time - 4 <= control_time <= t_time + 4):
                        continue

                    if control_repo in [c["repo_name"] for c in matched_controls]:
                        continue

                    # 验证控制组的离职事件
                    treatment_weeks = control_treatment_weeks.get(control_repo, [])
                    if treatment_weeks:
                        window_start = control_time - timewindow_weeks
                        window_end = control_time + timewindow_weeks

                        import bisect
                        left = bisect.bisect_left(treatment_weeks, window_start)
                        right = bisect.bisect_right(treatment_weeks, window_end)
                        if left < right:
                            continue

                    matched_controls.append({
                        "repo_name": control_repo,
                        "matched_time": control_time,
                        "features": control_row[feature_columns].values,
                    })
                    used_time_keys.add(control_row["time_key"])

                # 如果这次迭代没有找到新的匹配，提前退出
                if len(matched_controls) == previous_matched_count:
                    if logger:
                        logger.warning(f"No new matches found for {t_repo} in iteration {iteration_count}, stopping search")
                    break

                neighbor_query_size *= 2

            if matched_controls:
                matched_pairs[t_burst] = {
                    "burst": t_burst,
                    "repo_name": t_repo,
                    "treatment_time": t_time,
                    "controls": matched_controls,
                    "treatment_features": t_row[feature_columns].values,
                    "control_features": np.array([c["features"] for c in matched_controls]),
                }
                logging.info(f"Matched burst:{t_burst}-repo:{t_repo} with {len(matched_controls)} controls")
            else:
                logging.warning(f"No valid controls for {t_repo}")

            processed_cases += 1

            # 每处理100个案例记录一次进度
            if processed_cases % 100 == 0:
                logger.info(f"Processed {processed_cases}/{total_treatment_cases} treatment cases")

        # 批次处理完成后进行垃圾回收
        batch_time = time.time() - batch_start_time
        logger.info(f"Completed batch {batch_idx + 1} in {batch_time:.2f}s")
        gc.collect()

        # 检查是否超时，如果超时则退出外层循环
        if time.time() - function_start_time > max_function_time:
            logger.error(f"Function timeout, breaking out of batch processing")
            break

    logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")

    # 清理中间变量
    del control_treatment_weeks
    gc.collect()

    return matched_pairs, treatment_features_df, control_data_indexed

@memory_and_time_monitor
def compile_data_from_matched_pairs(
    matched_pairs: Dict,
    productivity: pd.DataFrame,
    window_size: int,
    batch_size: int = 500,  # 减小批次大小以节省内存
    log_interval: int = 500,
    logger=None
) -> pd.DataFrame:
    """从匹配结果编译数据 - 内存和时间优化版本"""
    if logger is None:
        logger = logging.getLogger()

    # 初始化跟踪器
    memory_tracker = MemoryTracker(logger)
    time_tracker = TimeTracker(logger)

    time_tracker.start("data_compilation")
    memory_tracker.log_memory("Compilation start")

    logger.info(f"Starting compilation of {len(matched_pairs)} matched pairs")

    time_tracker.start("productivity_grouping")
    # 为提高性能, 先对productivity按repo_name进行分组
    repo_groups = dict(tuple(productivity.groupby('repo_name')))
    memory_tracker.log_memory("After productivity grouping")
    time_tracker.end("productivity_grouping")

    # 批处理键值以减少内存使用
    all_keys = list(matched_pairs.keys())
    total_batches = (len(all_keys) + batch_size - 1) // batch_size
    logger.info(f"Processing in {total_batches} batches of size {batch_size}")

    all_data = []
    processed = 0

    time_tracker.start("batch_processing")
    for batch_idx in range(total_batches):
        batch_start_time = time.time()
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(all_keys))
        batch_keys = all_keys[start_idx:end_idx]

        batch_data = []

        for item_key in batch_keys:
            matched_data = matched_pairs[item_key]
            repo_name = matched_data["repo_name"]
            treatment_time = matched_data["treatment_time"]
            control_groups = matched_data["controls"]

            # 只在repo_groups里有数据时处理
            if repo_name in repo_groups:
                # 处理treatment数据 - 使用预先分组的数据
                repo_data = repo_groups[repo_name]
                treatment_mask = (
                    (repo_data['standardized_time_weeks'] >= treatment_time - window_size) &
                    (repo_data['standardized_time_weeks'] <= treatment_time + window_size)
                )
                treatment_productivity = repo_data[treatment_mask].copy()

                if not treatment_productivity.empty:
                    treatment_productivity['relativized_time'] = treatment_productivity['standardized_time_weeks'] - treatment_time
                    treatment_productivity['is_treated'] = 1
                    treatment_productivity['post_treatment'] = treatment_productivity['relativized_time'] > 0
                    treatment_productivity['cohort_id'] = processed
                    batch_data.append(treatment_productivity)

                    # 处理control数据
                    for c in control_groups:
                        control_repo = c['repo_name']
                        control_time = c['matched_time']

                        if control_repo in repo_groups:
                            control_repo_data = repo_groups[control_repo]
                            control_mask = (
                                (control_repo_data['standardized_time_weeks'] >= control_time - window_size) &
                                (control_repo_data['standardized_time_weeks'] <= control_time + window_size)
                            )
                            control_data = control_repo_data[control_mask].copy()

                            if not control_data.empty:
                                control_data['relativized_time'] = control_data['standardized_time_weeks'] - control_time
                                control_data['is_treated'] = 0
                                control_data['post_treatment'] = control_data['relativized_time'] > 0
                                control_data['cohort_id'] = processed
                                batch_data.append(control_data)

                    processed += 1

            # 每处理一定数量记录日志
            if processed % log_interval == 0 and processed > 0:
                elapsed = time.time() - batch_start_time
                avg_time_per_item = elapsed / len(batch_keys) if len(batch_keys) > 0 else 0
                remaining_items = len(matched_pairs) - processed
                eta_seconds = remaining_items * avg_time_per_item
                eta_minutes = eta_seconds / 60
                logger.info(f"[PROGRESS] Processed {processed}/{len(matched_pairs)} matched pairs, ETA: {eta_minutes:.1f}min")
                memory_tracker.log_memory(f"After processing {processed} pairs")

        # 合并当前批次并添加到结果
        if batch_data:
            batch_result = pd.concat(batch_data, ignore_index=True)
            all_data.append(batch_result)

            # 清理内存
            del batch_data
            del batch_result
            gc.collect()

        batch_time = time.time() - batch_start_time
        logger.info(f"[BATCH] Completed batch {batch_idx+1}/{total_batches} in {batch_time:.2f}s, total processed: {processed}")
        memory_tracker.log_memory(f"After batch {batch_idx+1}")

    time_tracker.end("batch_processing")

    time_tracker.start("final_concatenation")
    # 合并所有批次数据
    if all_data:
        logger.info(f"Concatenating all {len(all_data)} batches")
        final_data = pd.concat(all_data, ignore_index=True)

        # 清理中间数据
        del all_data
        gc.collect()
        memory_tracker.log_memory("After final concatenation")

        logger.info(f"Final compiled data shape: {final_data.shape}")
        time_tracker.end("final_concatenation")
        time_tracker.end("data_compilation")

        # 输出时间统计
        time_summary = time_tracker.get_summary()
        logger.info("=== COMPILATION TIME SUMMARY ===")
        for stage, duration in time_summary.items():
            logger.info(f"{stage}: {duration:.2f}s")
        logger.info("=================================")

        return final_data
    else:
        logger.warning("No data to compile")
        time_tracker.end("final_concatenation")
        time_tracker.end("data_compilation")
        return pd.DataFrame()

def process_single_limit(limit):
    """处理单个limit的PSM匹配 - 内存和时间优化版本"""
    # 设置独立的logger
    logger = setup_logging_for_limit(limit)

    # 初始化跟踪器
    memory_tracker = MemoryTracker(logger)
    time_tracker = TimeTracker(logger)

    overall_start_time = time.time()
    time_tracker.start("overall_processing")
    memory_tracker.log_memory("Process start")

    print(f"\n{'='*60}")
    print(f"STARTING PSM MATCHING FOR LIMIT: {limit} (IMPROVED)")
    print(f"{'='*60}")
    logger.info(f"Starting PSM matching for limit: {limit} (IMPROVED)")

    # 设置输出目录
    input_dir = f'../result/20250730_did_result/'
    output_dir = f'../result/20250730_did_result_psm_matching_improved/'
    os.makedirs(output_dir, exist_ok=True)

    time_tracker.start("data_loading")
    # 读取对应limit的生产力数据
    productivity_file = f'{input_dir}productivity_with_propensity_scores_with_attritions_{limit}.csv'

    if not os.path.exists(productivity_file):
        error_msg = f"Productivity file not found: {productivity_file}"
        print(f"Error: {error_msg}")
        logger.error(error_msg)
        return

    print(f"Reading productivity data from: {productivity_file}")
    logger.info(f"Reading productivity data from: {productivity_file}")
    productivity = pd.read_csv(productivity_file)
    print(f"Loaded productivity data shape: {productivity.shape}")
    logger.info(f"Loaded productivity data shape: {productivity.shape}")
    memory_tracker.log_memory("After loading productivity data")
    time_tracker.end("data_loading")

    time_tracker.start("data_preprocessing")
    # 转换类型
    productivity["standardized_time_weeks"] = productivity["standardized_time_weeks"].astype(int)
    memory_tracker.log_memory("After data type conversion")
    time_tracker.end("data_preprocessing")

    # 获取attrition数据 - 完全按照原始逻辑
    window_sizes = [12]
    for window in window_sizes:
        time_tracker.start(f"window_{window}_processing")
        logger.info(f"Processing window size: {window}")

        time_tracker.start("attrition_filtering")
        attritions = productivity[productivity['someone_left'] == 1].copy()
        attritions = attritions[attritions['feature_sigmod_add'].notnull()]
        attritions = attritions[attritions['feature_sigmod_add'] != 0.5]
        print(f"In window {window}, attritions shape is {attritions.shape}")
        logger.info(f"In window {window}, attritions shape is {attritions.shape}")
        memory_tracker.log_memory("After attrition filtering")
        time_tracker.end("attrition_filtering")

        time_tracker.start("data_fillna")
        attritions = attritions.fillna(0)
        productivity = productivity.fillna(0)
        memory_tracker.log_memory("After fillna")
        time_tracker.end("data_fillna")

        # 转换类型
        if 'burst' in attritions.columns:
            attritions['burst'] = attritions['burst'].astype(int)

        time_tracker.start("psm_matching")
        logger.info("Starting PSM matching...")
        # 调用优化后的PSM函数进行匹配
        matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
            attritions,
            list(set(productivity['repo_name'])),
            productivity,
            n_neighbors=5,
            timewindow_weeks=window,
            feature_columns=[f'feature_sigmod_add'],
            extra_candidates=10,  # 保持原有参数
            batch_size=1000,  # 添加批处理大小参数
            logger=logger
        )
        time_tracker.end("psm_matching")
        memory_tracker.log_memory("After PSM matching")

        logger.info(f"Finished PSM matching with {len(matched_pairs)} matched pairs, starting to compile data...")

        time_tracker.start("save_matched_pairs")
        # --- 保存matched_pairs的中间结果 ---
        matched_pairs_file = output_dir + f"matched_pairs_limit{limit}_window{window}_improved.pkl"
        try:
            with open(matched_pairs_file, "wb") as f:
                pickle.dump(matched_pairs, f)
            logger.info(f"Matched pairs saved to {matched_pairs_file}")
        except Exception as e:
            logger.error(f"Failed to save matched_pairs to {matched_pairs_file}: {e}")
        time_tracker.end("save_matched_pairs")

        time_tracker.start("data_compilation")
        # 使用优化的函数编译数据
        compiled_data = compile_data_from_matched_pairs(
            matched_pairs,
            productivity,
            window,
            batch_size=500,  # 减小批次大小
            log_interval=500,
            logger=logger
        )
        time_tracker.end("data_compilation")
        memory_tracker.log_memory("After data compilation")

        if not compiled_data.empty:
            time_tracker.start("final_data_processing")
            compiled_data_test = compiled_data.copy()
            compiled_data_test['is_post_treatment'] = compiled_data_test['post_treatment'].astype(int)
            compiled_data_test['is_treated'] = compiled_data_test['is_treated'].astype(int)
            compiled_data_test['is_treated_post_treatment'] = compiled_data_test['is_treated'] * compiled_data_test['post_treatment']
            time_tracker.end("final_data_processing")

            time_tracker.start("save_final_data")
            output_file = output_dir + f"compiled_data_test_limit{limit}_improved.csv"
            compiled_data_test.to_csv(output_file, index=False)
            print(f"Limit {limit}: Compiled data saved to {output_file}")
            logger.info(f"Compiled data saved to {output_file}")
            time_tracker.end("save_final_data")
        else:
            print(f"Limit {limit}: No compiled data")
            logger.warning(f"No compiled data for limit {limit}")

        time_tracker.end(f"window_{window}_processing")

        # 清理内存
        del attritions, matched_pairs, compiled_data
        if 'compiled_data_test' in locals():
            del compiled_data_test
        gc.collect()
        memory_tracker.log_memory("After window cleanup")

    time_tracker.end("overall_processing")
    total_time = time.time() - overall_start_time

    # 输出详细的时间统计
    time_summary = time_tracker.get_summary()
    logger.info("=== DETAILED TIME SUMMARY ===")
    for stage, duration in time_summary.items():
        percentage = (duration / total_time) * 100
        logger.info(f"{stage}: {duration:.2f}s ({percentage:.1f}%)")
    logger.info("==============================")

    print(f"\n{'='*60}")
    print(f"COMPLETED PSM MATCHING FOR LIMIT: {limit} (IMPROVED)")
    print(f"Total processing time: {total_time:.1f} seconds")
    print(f"Peak memory usage: {memory_tracker.peak_memory:.1f}MB")
    print(f"{'='*60}\n")
    logger.info(f"Completed PSM matching for limit: {limit} (IMPROVED), Total processing time: {total_time:.1f} seconds, Peak memory: {memory_tracker.peak_memory:.1f}MB")

    # 最终清理内存
    del productivity, treatment_features_df, control_features_df
    gc.collect()

    return limit, total_time, memory_tracker.peak_memory

def main():
    """主函数，并行处理所有attrition limits - 内存和时间优化版本"""
    overall_start_time = time.time()
    print(f"\n{'='*80}")
    print("STARTING PARALLEL PSM MATCHING FOR MULTIPLE LIMITS (IMPROVED)")
    print(f"Processing limits: {ATTRITION_LIMITS}")
    print(f"Using {min(len(ATTRITION_LIMITS), N_CPUS)} processes")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*80}")

    # 创建主日志记录器
    main_logger = logging.getLogger('main_improved')
    main_logger.setLevel(logging.INFO)

    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [MAIN] %(levelname)s: %(message)s")
    console_handler.setFormatter(formatter)
    main_logger.addHandler(console_handler)

    main_logger.info(f"Starting parallel processing of {len(ATTRITION_LIMITS)} limits")

    # 使用ProcessPoolExecutor进行并行处理
    max_workers = min(len(ATTRITION_LIMITS), N_CPUS)

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_limit = {executor.submit(process_single_limit, limit): limit for limit in ATTRITION_LIMITS}

        # 收集结果
        completed_limits = []
        for future in as_completed(future_to_limit):
            limit = future_to_limit[future]
            try:
                limit_result, processing_time, peak_memory = future.result()
                completed_limits.append((limit_result, processing_time, peak_memory))
                completion_time = datetime.now().strftime('%H:%M:%S')
                print(f"[{completion_time}] Limit {limit_result} completed in {processing_time:.1f}s (Peak memory: {peak_memory:.1f}MB)")
                main_logger.info(f"Limit {limit_result} completed in {processing_time:.1f}s (Peak memory: {peak_memory:.1f}MB)")
            except Exception as exc:
                error_time = datetime.now().strftime('%H:%M:%S')
                print(f"[{error_time}] Limit {limit} generated an exception: {exc}")
                main_logger.error(f"Limit {limit} generated an exception: {exc}")

    # 按原始顺序排序结果
    completed_limits.sort(key=lambda x: ATTRITION_LIMITS.index(x[0]))

    overall_processing_time = time.time() - overall_start_time
    end_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    print(f"\n{'='*80}")
    print("PARALLEL PSM MATCHING FOR ALL LIMITS COMPLETED (IMPROVED)")
    print(f"End time: {end_time}")
    print(f"Total processing time: {overall_processing_time:.1f} seconds ({overall_processing_time/60:.1f} minutes)")
    print("Individual processing results:")

    total_individual_time = 0
    max_memory = 0
    for limit, processing_time, peak_memory in completed_limits:
        total_individual_time += processing_time
        max_memory = max(max_memory, peak_memory)
        print(f"  Limit {limit}: {processing_time:.1f}s ({processing_time/60:.1f}min), Peak memory: {peak_memory:.1f}MB")

    efficiency = (total_individual_time / overall_processing_time) if overall_processing_time > 0 else 0
    print(f"\nParallel efficiency: {efficiency:.2f}x")
    print(f"Maximum memory usage across all processes: {max_memory:.1f}MB")
    print(f"{'='*80}")

    main_logger.info(f"All limits completed. Total time: {overall_processing_time:.1f}s, Efficiency: {efficiency:.2f}x, Max memory: {max_memory:.1f}MB")

if __name__ == "__main__":
    print(f"Script started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python version: {sys.version}")
    print(f"Available memory: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f}GB")
    print(f"Available CPU cores: {N_CPUS}")

    try:
        main()
    except KeyboardInterrupt:
        print("\nScript interrupted by user")
    except Exception as e:
        print(f"\nScript failed with error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print(f"Script completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        # 最终内存清理
        gc.collect()
