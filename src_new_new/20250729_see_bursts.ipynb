{"cells": [{"cell_type": "code", "execution_count": 2, "id": "a5a7b221", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "\n", "attrition_with_burst_original = pd.read_csv('../data/attritions_20250227_add_burst_merged.csv')\n", "attrition_with_burst_original_without_merge = pd.read_csv('../data/attritions_20250227_add_burst.csv')\n", "attrition_with_burst_new = pd.read_csv('../data/attrition_csv/attritions_add_burst_merged_365.csv')\n", "# attrition_with_burst_without_merge  = pd.read_csv('../data/attrition_csv/attrition_burst_core_merged_dev_365.csv')\n", "\n", "p_test = pd.read_csv('../result/p_test.csv')\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "9ab211d2", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "pr_throughput", "rawType": "float64", "type": "float"}, {"name": "rolling_slope", "rawType": "float64", "type": "float"}, {"name": "rolling_mean", "rawType": "float64", "type": "float"}, {"name": "rolling_rate_of_change", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_add", "rawType": "float64", "type": "float"}, {"name": "feature_sigmod_multiply", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "burst", "rawType": "float64", "type": "float"}, {"name": "attrition_count", "rawType": "float64", "type": "float"}, {"name": "mainLanguage", "rawType": "object", "type": "string"}, {"name": "createdAt_standardized", "rawType": "int64", "type": "integer"}, {"name": "duration", "rawType": "int64", "type": "integer"}], "ref": "cbc8b9a4-76fd-4296-98d5-37a16882aa76", "rows": [["0", "01mf02/jaq", "611", "2.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "76"], ["1", "01mf02/jaq", "612", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "77"], ["2", "01mf02/jaq", "613", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "78"], ["3", "01mf02/jaq", "614", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "79"], ["4", "01mf02/jaq", "615", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "80"], ["5", "01mf02/jaq", "616", "1.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "81"], ["6", "01mf02/jaq", "617", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "82"], ["7", "01mf02/jaq", "618", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "83"], ["8", "01mf02/jaq", "619", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "84"], ["9", "01mf02/jaq", "620", "1.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "85"], ["10", "01mf02/jaq", "621", "0.0", null, null, null, null, null, "0", null, null, null, null, null, "Rust", "535", "86"], ["11", "01mf02/jaq", "622", "0.0", "-0.0559440559440559", "0.3333333333333333", null, null, null, "0", null, null, null, null, null, "Rust", "535", "87"], ["12", "01mf02/jaq", "623", "1.0", "0.0454545454545454", "0.25", "-0.4054651081081645", "0.4612118154493698", "0.4746801074469995", "0", null, null, null, null, null, "Rust", "535", "88"], ["13", "01mf02/jaq", "624", "0.0", "0.0244755244755244", "0.25", "0.0", "0.5621765008857981", "0.5", "0", null, null, null, null, null, "Rust", "535", "89"], ["14", "01mf02/jaq", "625", "0.0", "0.0034965034965034", "0.25", "0.0", "0.5621765008857981", "0.5", "0", null, null, null, null, null, "Rust", "535", "90"], ["15", "01mf02/jaq", "626", "0.0", "-0.0174825174825174", "0.25", "0.0", "0.5621765008857981", "0.5", "0", null, null, null, null, null, "Rust", "535", "91"], ["16", "01mf02/jaq", "627", "0.0", "-0.0384615384615384", "0.25", "0.0", "0.5621765008857981", "0.5", "0", null, null, null, null, null, "Rust", "535", "92"], ["17", "01mf02/jaq", "628", "0.0", "-0.0139860139860139", "0.1666666666666666", "-0.6931471805599453", "0.3713381256924368", "0.471150945100074", "0", null, null, null, null, null, "Rust", "535", "93"], ["18", "01mf02/jaq", "629", "0.0", "-0.0279720279720279", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "94"], ["19", "01mf02/jaq", "630", "0.0", "-0.0419580419580419", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "95"], ["20", "01mf02/jaq", "631", "0.0", "-0.0559440559440559", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "96"], ["21", "01mf02/jaq", "632", "1.0", "0.0139860139860139", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "97"], ["22", "01mf02/jaq", "633", "0.0", "0.0", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "98"], ["23", "01mf02/jaq", "634", "0.0", "-0.0139860139860139", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "99"], ["24", "01mf02/jaq", "635", "0.0", "0.0174825174825174", "0.0833333333333333", "-0.6931471805599453", "0.3521016630529235", "0.4855634474533289", "0", null, null, null, null, null, "Rust", "535", "100"], ["25", "01mf02/jaq", "636", "0.0", "0.0104895104895104", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "101"], ["26", "01mf02/jaq", "637", "0.0", "0.0034965034965034", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "102"], ["27", "01mf02/jaq", "638", "0.0", "-0.0034965034965034", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "103"], ["28", "01mf02/jaq", "639", "0.0", "-0.0104895104895104", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "104"], ["29", "01mf02/jaq", "640", "0.0", "-0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "105"], ["30", "01mf02/jaq", "641", "0.0", "-0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "106"], ["31", "01mf02/jaq", "642", "0.0", "-0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "107"], ["32", "01mf02/jaq", "643", "0.0", "-0.0384615384615384", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "108"], ["33", "01mf02/jaq", "644", "0.0", "0.0", "0.0", "-0.6931471805599453", "0.3333333333333333", "0.5", "0", null, null, null, null, null, "Rust", "535", "109"], ["34", "01mf02/jaq", "645", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", null, null, null, null, null, "Rust", "535", "110"], ["35", "01mf02/jaq", "646", "0.0", "0.0", "0.0", "0.0", "0.5", "0.5", "0", null, null, null, null, null, "Rust", "535", "111"], ["36", "01mf02/jaq", "647", "1.0", "0.0384615384615384", "0.0833333333333333", "0.6931471805599453", "0.6849210888642885", "0.514436552546671", "0", null, null, null, null, null, "Rust", "535", "112"], ["37", "01mf02/jaq", "648", "0.0", "0.0314685314685314", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "113"], ["38", "01mf02/jaq", "649", "0.0", "0.0244755244755244", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "114"], ["39", "01mf02/jaq", "650", "0.0", "0.0174825174825174", "0.0833333333333333", "0.0", "0.520821285372743", "0.5", "0", null, null, null, null, null, "Rust", "535", "115"], ["40", "01mf02/jaq", "651", "1.0", "0.0489510489510489", "0.1666666666666666", "0.6931471805599453", "0.7026217602281838", "0.5288490548999261", "0", null, null, null, null, null, "Rust", "535", "116"], ["41", "01mf02/jaq", "652", "0.0", "0.0349650349650349", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "117"], ["42", "01mf02/jaq", "653", "0.0", "0.0209790209790209", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "118"], ["43", "01mf02/jaq", "654", "0.0", "0.0069930069930069", "0.1666666666666666", "0.0", "0.5415704832167999", "0.5", "0", null, null, null, null, null, "Rust", "535", "119"], ["44", "01mf02/jaq", "655", "2.0", "0.0699300699300699", "0.3333333333333333", "1.0986122886681098", "0.8072042852066904", "0.5905414368138762", "0", null, null, null, null, null, "Rust", "535", "120"], ["45", "01mf02/jaq", "656", "0.0", "0.0419580419580419", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", null, null, null, null, null, "Rust", "535", "121"], ["46", "01mf02/jaq", "657", "1.0", "0.0524475524475524", "0.4166666666666667", "0.6931471805599453", "0.7520944051795897", "0.5717051007956732", "0", null, null, null, null, null, "Rust", "535", "122"], ["47", "01mf02/jaq", "658", "0.0", "0.0174825174825174", "0.4166666666666667", "0.0", "0.6026853379784917", "0.5", "1", "854.0", "0.1558441558441558", "228.0", "1.0", "1.0", "Rust", "535", "123"], ["48", "01mf02/jaq", "659", "0.0", "0.0279720279720279", "0.3333333333333333", "-0.6931471805599453", "0.4110046290252653", "0.4424933340244421", "0", null, null, null, null, null, "Rust", "535", "124"], ["49", "01mf02/jaq", "660", "0.0", "0.0", "0.3333333333333333", "0.0", "0.5825702064623147", "0.5", "0", null, null, null, null, null, "Rust", "535", "125"]], "shape": {"columns": 17, "rows": 16235133}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>pr_throughput</th>\n", "      <th>rolling_slope</th>\n", "      <th>rolling_mean</th>\n", "      <th>rolling_rate_of_change</th>\n", "      <th>feature_sigmod_add</th>\n", "      <th>feature_sigmod_multiply</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>mainLanguage</th>\n", "      <th>createdAt_standardized</th>\n", "      <th>duration</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>611</td>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Rust</td>\n", "      <td>535</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>612</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Rust</td>\n", "      <td>535</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>613</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Rust</td>\n", "      <td>535</td>\n", "      <td>78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>614</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Rust</td>\n", "      <td>535</td>\n", "      <td>79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>615</td>\n", "      <td>0.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Rust</td>\n", "      <td>535</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16235128</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>737</td>\n", "      <td>0.0</td>\n", "      <td>-0.080420</td>\n", "      <td>0.250000</td>\n", "      <td>0.000000</td>\n", "      <td>0.562177</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>C#</td>\n", "      <td>347</td>\n", "      <td>390</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16235129</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>738</td>\n", "      <td>0.0</td>\n", "      <td>-0.055944</td>\n", "      <td>0.166667</td>\n", "      <td>-0.693147</td>\n", "      <td>0.371338</td>\n", "      <td>0.471151</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>C#</td>\n", "      <td>347</td>\n", "      <td>391</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16235130</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>739</td>\n", "      <td>0.0</td>\n", "      <td>-0.069930</td>\n", "      <td>0.166667</td>\n", "      <td>0.000000</td>\n", "      <td>0.541570</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>C#</td>\n", "      <td>347</td>\n", "      <td>392</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16235131</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>740</td>\n", "      <td>0.0</td>\n", "      <td>-0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>-0.693147</td>\n", "      <td>0.352102</td>\n", "      <td>0.485563</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>C#</td>\n", "      <td>347</td>\n", "      <td>393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16235132</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>741</td>\n", "      <td>1.0</td>\n", "      <td>0.038462</td>\n", "      <td>0.083333</td>\n", "      <td>0.000000</td>\n", "      <td>0.520821</td>\n", "      <td>0.500000</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>C#</td>\n", "      <td>347</td>\n", "      <td>394</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16235133 rows × 17 columns</p>\n", "</div>"], "text/plain": ["                              repo_name  standardized_time_weeks  \\\n", "0                            01mf02/jaq                      611   \n", "1                            01mf02/jaq                      612   \n", "2                            01mf02/jaq                      613   \n", "3                            01mf02/jaq                      614   \n", "4                            01mf02/jaq                      615   \n", "...                                 ...                      ...   \n", "16235128  zzzprojects/html-agility-pack                      737   \n", "16235129  zzzprojects/html-agility-pack                      738   \n", "16235130  zzzprojects/html-agility-pack                      739   \n", "16235131  zzzprojects/html-agility-pack                      740   \n", "16235132  zzzprojects/html-agility-pack                      741   \n", "\n", "          pr_throughput  rolling_slope  rolling_mean  rolling_rate_of_change  \\\n", "0                   2.0            NaN           NaN                     NaN   \n", "1                   0.0            NaN           NaN                     NaN   \n", "2                   0.0            NaN           NaN                     NaN   \n", "3                   0.0            NaN           NaN                     NaN   \n", "4                   0.0            NaN           NaN                     NaN   \n", "...                 ...            ...           ...                     ...   \n", "16235128            0.0      -0.080420      0.250000                0.000000   \n", "16235129            0.0      -0.055944      0.166667               -0.693147   \n", "16235130            0.0      -0.069930      0.166667                0.000000   \n", "16235131            0.0      -0.038462      0.083333               -0.693147   \n", "16235132            1.0       0.038462      0.083333                0.000000   \n", "\n", "          feature_sigmod_add  feature_sigmod_multiply  someone_left  tenure  \\\n", "0                        NaN                      NaN             0     NaN   \n", "1                        NaN                      NaN             0     NaN   \n", "2                        NaN                      NaN             0     NaN   \n", "3                        NaN                      NaN             0     NaN   \n", "4                        NaN                      NaN             0     NaN   \n", "...                      ...                      ...           ...     ...   \n", "16235128            0.562177                 0.500000             0     NaN   \n", "16235129            0.371338                 0.471151             0     NaN   \n", "16235130            0.541570                 0.500000             0     NaN   \n", "16235131            0.352102                 0.485563             0     NaN   \n", "16235132            0.520821                 0.500000             0     NaN   \n", "\n", "          commit_percent  commits  burst  attrition_count mainLanguage  \\\n", "0                    NaN      NaN    NaN              NaN         Rust   \n", "1                    NaN      NaN    NaN              NaN         Rust   \n", "2                    NaN      NaN    NaN              NaN         Rust   \n", "3                    NaN      NaN    NaN              NaN         Rust   \n", "4                    NaN      NaN    NaN              NaN         Rust   \n", "...                  ...      ...    ...              ...          ...   \n", "16235128             NaN      NaN    NaN              NaN           C#   \n", "16235129             NaN      NaN    NaN              NaN           C#   \n", "16235130             NaN      NaN    NaN              NaN           C#   \n", "16235131             NaN      NaN    NaN              NaN           C#   \n", "16235132             NaN      NaN    NaN              NaN           C#   \n", "\n", "          createdAt_standardized  duration  \n", "0                            535        76  \n", "1                            535        77  \n", "2                            535        78  \n", "3                            535        79  \n", "4                            535        80  \n", "...                          ...       ...  \n", "16235128                     347       390  \n", "16235129                     347       391  \n", "16235130                     347       392  \n", "16235131                     347       393  \n", "16235132                     347       394  \n", "\n", "[16235133 rows x 17 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["p_test"]}, {"cell_type": "code", "execution_count": 3, "id": "65a6d07c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_id", "rawType": "int64", "type": "integer"}, {"name": "attrition_time", "rawType": "object", "type": "string"}, {"name": "dev_login", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "int64", "type": "integer"}], "ref": "a747b973-6ef3-48e0-b974-3512e7ef6a56", "rows": [["0", "01mf02/jaq", "1", "2023-04-11", "kammerchorinnsbruck", "2023-04-11", "1", "1", "False", null, "1", "854.0", "0.1558441558441558", "228"], ["1", "05bit/peewee-async", "1", "2020-09-25", "rudyryk", "2020-09-25", "2", "1", "False", null, "1", "2189.0", "0.5484848484848485", "181"], ["2", "05bit/peewee-async", "2", "2021-11-02", "rudyryk", "2021-11-02", "3", "1", "False", "403.0", "1", "2592.0", "0.5515151515151515", "182"], ["3", "0LNetworkCommunity/libra-legacy-v6", "1", "2020-04-14", "bothra90", "2020-04-14", "4", "1", "False", null, "1", "299.0", "0.0132705479452054", "93"], ["4", "0LNetworkCommunity/libra-legacy-v6", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "13", "1", "False", "397.0", "1", "581.0", "0.0204052511415525", "143"], ["5", "0b01/tectonicdb", "1", "2020-09-08", "0b01", "2020-09-08", "14", "1", "False", null, "1", "1070.0", "0.8469827586206896", "393"], ["6", "0b01/tectonicdb", "2", "2021-09-27", "0b01", "2021-09-27", "15", "1", "False", "384.0", "1", "1454.0", "0.8491379310344828", "394"], ["7", "0b01001001/spectree", "1", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "2023-03-22", "16", "1", "False", null, "1", "331.0", "0.0308483290488431", "12"], ["8", "0chain/0chain", "1", "2018-12-08", "sachin-0chain", "2018-12-08", "17", "1", "False", null, "1", "159.0", "0.0215907286870931", "272"], ["9", "0chain/0chain", "4", "2020-11-20", "IntegralTeam", "2020-11-20", "20", "1", "False", "478.0", "1", "288.0", "0.0536593110017463", "676"], ["10", "0lnetworkcommunity/libra", "1", "2020-04-14", "bothra90", "2020-04-14", "28", "1", "False", null, "1", "299.0", "0.0143055555555555", "103"], ["11", "0lnetworkcommunity/libra", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "37", "1", "False", "397.0", "1", "581.0", "0.02", "144"], ["12", "0rpc/zerorpc-python", "1", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "2012-04-10", "40", "1", "False", null, "1", "30.0", "0.038647342995169", "8"], ["13", "0rpc/zerorpc-python", "2", "2013-03-08", "lopter", "2013-03-08", "41", "1", "False", "332.0", "1", "338.0", "0.0434782608695652", "9"], ["14", "0rpc/zerorpc-python", "3", "2014-03-07", "jpetazzo", "2014-03-07", "42", "1", "False", "364.0", "1", "727.0", "0.0289855072463768", "6"], ["15", "0rpc/zerorpc-python", "4", "2018-02-02", "bombela", "2018-02-02", "43", "1", "False", "1428.0", "1", "2168.0", "0.5797101449275363", "120"], ["16", "0rpc/zerorpc-python", "5", "2019-06-26", "bombela", "2019-06-26", "44", "1", "False", "509.0", "1", "2677.0", "0.5845410628019324", "121"], ["17", "0x5bfa/FluentHub", "1", "2023-04-06", "Lamparter", "2023-04-06", "45", "1", "False", null, "1", "319.0", "0.0492813141683778", "24"], ["18", "0x7c13/notepads", "1", "2020-05-31", "Daxxxis", "2020-05-31", "46", "1", "False", null, "1", "83.0", "0.014069264069264", "13"], ["19", "0xPolygonZero/plonky2", "1", "2023-05-16", "typ3c4t", "2023-05-16", "47", "1", "False", null, "1", "292.0", "0.1144607843137254", "467"], ["20", "0xax/linux-insides", "6", "2017-01-11", "<PERSON><PERSON><PERSON>g", "2017-01-11", "53", "1", "False", "344.0", "1", "531.0", "0.0230891719745222", "29"], ["21", "0xax/linux-insides", "7", "2018-03-22", "<PERSON><PERSON><PERSON>g", "2018-03-22", "54", "1", "False", "435.0", "1", "966.0", "0.0302547770700636", "38"], ["22", "0xax/linux-insides", "8", "2019-03-08", "0xAX", "2019-03-08", "55", "1", "False", "351.0", "1", "1524.0", "0.3805732484076433", "478"], ["23", "0xax/linux-insides", "9", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-11-10", "56", "1", "False", "247.0", "1", "1036.0", "0.0063694267515923", "8"], ["24", "0xax/linux-insides", "10", "2020-05-31", "0xAX", "2020-05-31", "57", "1", "False", "203.0", "1", "1974.0", "0.3813694267515923", "479"], ["25", "0xax/linux-insides", "14", "2022-07-31", "0xAX", "2022-08-03", "61", "2", "False", "591.0", "1", "1454.5", "0.3925159235668789", "493"], ["26", "0xax/linux-insides", "16", "2023-01-09", "<PERSON><PERSON><PERSON>g", "2023-01-09", "62", "1", "False", "159.0", "1", "2720.0", "0.0326433121019108", "41"], ["27", "0xfe/vextab", "1", "2014-11-28", "0xfe", "2014-11-28", "63", "1", "False", null, "1", "719.0", "0.6614173228346457", "168"], ["28", "0xfe/vextab", "2", "2016-07-10", "0xfe", "2016-07-10", "64", "1", "False", "590.0", "1", "1309.0", "0.7716535433070866", "196"], ["29", "0xfe/vextab", "3", "2017-11-29", "0xfe", "2017-11-29", "65", "1", "False", "507.0", "1", "1816.0", "0.8031496062992126", "204"], ["30", "0xfe/vextab", "4", "2019-02-08", "0xfe", "2019-02-08", "66", "1", "False", "436.0", "1", "2252.0", "0.8070866141732284", "205"], ["31", "0xpolygon/polygon-edge", "1", "2019-11-15", "ferranbt", "2019-11-15", "67", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["32", "0xpolygon/polygon-edge", "2", "2021-05-07", "ferranbt", "2021-05-07", "68", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["33", "0xpolygon/polygon-edge", "3", "2022-03-31", "munna0908", "2022-03-31", "69", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["34", "0xpolygon/polygon-sdk", "1", "2019-11-15", "ferranbt", "2019-11-15", "75", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["35", "0xpolygon/polygon-sdk", "2", "2021-05-07", "ferranbt", "2021-05-07", "76", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["36", "0xpolygon/polygon-sdk", "3", "2022-03-31", "munna0908", "2022-03-31", "77", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["37", "0xpolygonhermez/zkevm-node", "1", "2023-10-03", "Psykepro", "2023-10-03", "83", "1", "False", null, "1", "347.0", "0.0675580577058409", "96"], ["38", "0xpolygonmiden/crypto", "1", "2023-04-05", "vlopes11", "2023-04-05", "84", "1", "False", null, "1", "166.0", "0.0728476821192053", "22"], ["39", "0xproject/0x-starter-project", "1", "2018-08-28", "fabioberger", "2018-08-28", "98", "1", "False", null, "1", "306.0", "0.102803738317757", "11"], ["40", "0xproject/0x-starter-project", "2", "2019-12-06", "dekz", "2019-12-06", "99", "1", "False", "465.0", "1", "605.0", "0.4766355140186916", "51"], ["41", "0xs34n/starknet.js", "1", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "2022-07-22", "100", "1", "False", null, "1", "50.0", "0.0313930673642903", "48"], ["42", "0xs34n/starknet.js", "2", "2023-03-08", "irisdv", "2023-03-08", "101", "1", "False", "229.0", "1", "88.0", "0.0222367560497056", "34"], ["43", "0xs34n/starknet.js", "3", "2023-12-07", "janek26", "2023-12-07", "102", "1", "False", "274.0", "1", "778.0", "0.1366906474820144", "209"], ["44", "0xsky/xredis", "1", "2021-01-24", "0xsky", "2021-01-24", "103", "1", "False", null, "1", "2392.0", "0.6623376623376623", "153"], ["45", "0xsky/xredis", "2", "2022-03-22", "ox<PERSON>", "2022-03-22", "104", "1", "False", "422.0", "1", "1253.0", "0.2251082251082251", "52"], ["46", "0xsky/xredis", "3", "2023-06-13", "0xsky", "2023-06-13", "105", "1", "False", "448.0", "1", "3262.0", "0.6666666666666666", "154"], ["47", "0xspaceshard/starknet-hardhat-plugin", "1", "2022-03-17", "dribeiro-Shard<PERSON>abs", "2022-03-17", "106", "1", "False", null, "1", "104.0", "0.0857908847184986", "32"], ["48", "1-liners/1-liners", "1", "2015-07-05", "s<PERSON><PERSON><PERSON><PERSON>", "2015-07-05", "107", "1", "False", null, "1", "76.0", "0.2868852459016393", "105"], ["49", "101loop/drf-user", "1", "2019-04-02", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-04-02", "108", "1", "False", null, "1", "636.0", "0.40625", "52"]], "shape": {"columns": 13, "rows": 91248}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_id</th>\n", "      <th>attrition_time</th>\n", "      <th>dev_login</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>1</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>2023-04-11</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>854.0</td>\n", "      <td>0.155844</td>\n", "      <td>228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>1</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2020-09-25</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>2189.0</td>\n", "      <td>0.548485</td>\n", "      <td>181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>2</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2021-11-02</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "      <td>1</td>\n", "      <td>2592.0</td>\n", "      <td>0.551515</td>\n", "      <td>182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>1</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>2020-04-14</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>299.0</td>\n", "      <td>0.013271</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>21</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>2021-12-21</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "      <td>1</td>\n", "      <td>581.0</td>\n", "      <td>0.020405</td>\n", "      <td>143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91243</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>2</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-08-13</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "      <td>1</td>\n", "      <td>79.0</td>\n", "      <td>0.063158</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91244</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>3</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>2022-10-13</td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "      <td>1</td>\n", "      <td>1806.0</td>\n", "      <td>0.210526</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91245</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>1</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>2017-07-31</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>27.0</td>\n", "      <td>0.016432</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91246</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>2</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-11-09</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "      <td>1</td>\n", "      <td>87.0</td>\n", "      <td>0.023474</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91247</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>2019-10-02</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "      <td>1</td>\n", "      <td>820.0</td>\n", "      <td>0.018779</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>91248 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                        repo_name  attrition_id  \\\n", "0                                      01mf02/jaq             1   \n", "1                              05bit/peewee-async             1   \n", "2                              05bit/peewee-async             2   \n", "3              0LNetworkCommunity/libra-legacy-v6             1   \n", "4              0LNetworkCommunity/libra-legacy-v6            21   \n", "...                                           ...           ...   \n", "91243  zzzprojects/entityframework.dynamicfilters             2   \n", "91244  zzzprojects/entityframework.dynamicfilters             3   \n", "91245               zzzprojects/html-agility-pack             1   \n", "91246               zzzprojects/html-agility-pack             2   \n", "91247               zzzprojects/html-agility-pack             3   \n", "\n", "      attrition_time            dev_login attrition_date   burst  \\\n", "0         2023-04-11  kammerchorinnsbruck     2023-04-11       1   \n", "1         2020-09-25              rudyryk     2020-09-25       2   \n", "2         2021-11-02              rudyryk     2021-11-02       3   \n", "3         2020-04-14             bothra90     2020-04-14       4   \n", "4         2021-12-21          gregnazario     2021-12-21      13   \n", "...              ...                  ...            ...     ...   \n", "91243     2018-08-13         stgelaisalex     2018-08-13  157916   \n", "91244     2022-10-13       JonathanMagnan     2022-10-13  157917   \n", "91245     2017-07-31             waqasm78     2017-07-31  157918   \n", "91246     2018-11-09         stgelaisalex     2018-11-09  157919   \n", "91247     2019-10-02             waqasm78     2019-10-02  157920   \n", "\n", "       attrition_count  gap_less_than_84  inter_burst_gap  someone_left  \\\n", "0                    1             False              NaN             1   \n", "1                    1             False              NaN             1   \n", "2                    1             False            403.0             1   \n", "3                    1             False              NaN             1   \n", "4                    1             False            397.0             1   \n", "...                ...               ...              ...           ...   \n", "91243                1             False            384.0             1   \n", "91244                1             False           1522.0             1   \n", "91245                1             False              NaN             1   \n", "91246                1             False            466.0             1   \n", "91247                1             False            327.0             1   \n", "\n", "       tenure  commit_percent  commits  \n", "0       854.0        0.155844      228  \n", "1      2189.0        0.548485      181  \n", "2      2592.0        0.551515      182  \n", "3       299.0        0.013271       93  \n", "4       581.0        0.020405      143  \n", "...       ...             ...      ...  \n", "91243    79.0        0.063158       12  \n", "91244  1806.0        0.210526       40  \n", "91245    27.0        0.016432        7  \n", "91246    87.0        0.023474       10  \n", "91247   820.0        0.018779        8  \n", "\n", "[91248 rows x 13 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a = pd.read_csv('../data/attrition_csv/attrition_burst_core_dev_merged_365.csv')\n", "a"]}, {"cell_type": "code", "execution_count": 24, "id": "059a2a5c", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_id", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "standardized_time_weeks", "rawType": "int64", "type": "integer"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}], "ref": "e94b7784-ee26-45dc-ba9a-7075c9f279cf", "rows": [["0", "67c03791a55e5dd650be5caa", "01mf02/jaq", "attrition", "2023-04-11", "kammerchorinnsbruck", "1", "1", "False", null, "854.0", "0.1558441558441558", "228.0", "658", "1"], ["1", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2020-09-25", "rudyryk", "2", "1", "False", null, "2189.0", "0.5484848484848485", "181.0", "525", "1"], ["2", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2021-11-02", "rudyryk", "3", "1", "False", "403.0", "2592.0", "0.5515151515151515", "182.0", "583", "1"], ["3", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-04-14", "bothra90", "4", "1", "False", null, "299.0", "0.0132705479452054", "93.0", "502", "1"], ["4", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "13", "1", "False", "397.0", "581.0", "0.0204052511415525", "143.0", "590", "1"], ["5", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2020-09-08", "0b01", "14", "1", "False", null, "1070.0", "0.8469827586206896", "393.0", "523", "1"], ["6", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2021-09-27", "0b01", "15", "1", "False", "384.0", "1454.0", "0.8491379310344828", "394.0", "578", "1"], ["7", "67c03791a55e5dd650be5cb0", "0b01001001/spectree", "attrition", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "16", "1", "False", null, "331.0", "0.0308483290488431", "12.0", "655", "1"], ["8", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2018-12-08", "sachin-0chain", "17", "1", "False", null, "159.0", "0.0215907286870931", "272.0", "431", "1"], ["9", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2020-11-20", "IntegralTeam", "20", "1", "False", "478.0", "288.0", "0.0536593110017463", "676.0", "533", "1"], ["10", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-04-14", "bothra90", "28", "1", "False", null, "299.0", "0.0143055555555555", "103.0", "502", "1"], ["11", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "37", "1", "False", "397.0", "581.0", "0.02", "144.0", "590", "1"], ["12", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "40", "1", "False", null, "30.0", "0.038647342995169", "8.0", "84", "1"], ["13", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2013-03-08", "lopter", "41", "1", "False", "332.0", "338.0", "0.0434782608695652", "9.0", "131", "1"], ["14", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2014-03-07", "jpetazzo", "42", "1", "False", "364.0", "727.0", "0.0289855072463768", "6.0", "183", "1"], ["15", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2018-02-02", "bombela", "43", "1", "False", "1428.0", "2168.0", "0.5797101449275363", "120.0", "387", "1"], ["16", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2019-06-26", "bombela", "44", "1", "False", "509.0", "2677.0", "0.5845410628019324", "121.0", "460", "1"], ["17", "67c03791a55e5dd650be5cb9", "0x5bfa/FluentHub", "attrition", "2023-04-06", "Lamparter", "45", "1", "False", null, "319.0", "0.0492813141683778", "24.0", "657", "1"], ["18", "67c03791a55e5dd650be5cba", "0x7c13/notepads", "attrition", "2020-05-31", "Daxxxis", "46", "1", "False", null, "83.0", "0.014069264069264", "13.0", "508", "1"], ["19", "67c03791a55e5dd650be5cbc", "0xPolygonZero/plonky2", "attrition", "2023-05-16", "typ3c4t", "47", "1", "False", null, "292.0", "0.1144607843137254", "467.0", "663", "1"], ["20", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2017-01-11", "<PERSON><PERSON><PERSON>g", "53", "1", "False", "344.0", "531.0", "0.0230891719745222", "29.0", "332", "1"], ["21", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2018-03-22", "<PERSON><PERSON><PERSON>g", "54", "1", "False", "435.0", "966.0", "0.0302547770700636", "38.0", "394", "1"], ["22", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2019-03-08", "0xAX", "55", "1", "False", "351.0", "1524.0", "0.3805732484076433", "478.0", "444", "1"], ["23", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "56", "1", "False", "247.0", "1036.0", "0.0063694267515923", "8.0", "479", "1"], ["24", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2020-05-31", "0xAX", "57", "1", "False", "203.0", "1974.0", "0.3813694267515923", "479.0", "508", "1"], ["25", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2022-08-03", "0xAX", "61", "2", "False", "591.0", "1454.5", "0.3925159235668789", "493.0", "622", "1"], ["26", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2023-01-09", "<PERSON><PERSON><PERSON>g", "62", "1", "False", "159.0", "2720.0", "0.0326433121019108", "41.0", "645", "1"], ["27", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2014-11-28", "0xfe", "63", "1", "False", null, "719.0", "0.6614173228346457", "168.0", "221", "1"], ["28", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2016-07-10", "0xfe", "64", "1", "False", "590.0", "1309.0", "0.7716535433070866", "196.0", "305", "1"], ["29", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2017-11-29", "0xfe", "65", "1", "False", "507.0", "1816.0", "0.8031496062992126", "204.0", "378", "1"], ["30", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2019-02-08", "0xfe", "66", "1", "False", "436.0", "2252.0", "0.8070866141732284", "205.0", "440", "1"], ["31", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2019-11-15", "ferranbt", "67", "1", "False", null, "374.0", "0.1977217249796582", "243.0", "480", "1"], ["32", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2021-05-07", "ferranbt", "68", "1", "False", "539.0", "913.0", "0.2213181448331977", "272.0", "557", "1"], ["33", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2022-03-31", "munna0908", "69", "1", "False", "328.0", "230.0", "0.0349877949552481", "43.0", "604", "1"], ["34", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2019-11-15", "ferranbt", "75", "1", "False", null, "374.0", "0.1977217249796582", "243.0", "480", "1"], ["35", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2021-05-07", "ferranbt", "76", "1", "False", "539.0", "913.0", "0.2213181448331977", "272.0", "557", "1"], ["36", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2022-03-31", "munna0908", "77", "1", "False", "328.0", "230.0", "0.0349877949552481", "43.0", "604", "1"], ["37", "67c03791a55e5dd650be5cc6", "0xpolygonhermez/zkevm-node", "attrition", "2023-10-03", "Psykepro", "83", "1", "False", null, "347.0", "0.0675580577058409", "96.0", "683", "1"], ["38", "67c03791a55e5dd650be5cc8", "0xpolygonmiden/crypto", "attrition", "2023-04-05", "vlopes11", "84", "1", "False", null, "166.0", "0.0728476821192053", "22.0", "657", "1"], ["39", "67c03791a55e5dd650be5ccd", "0xproject/0x-starter-project", "attrition", "2018-08-28", "fabioberger", "98", "1", "False", null, "306.0", "0.102803738317757", "11.0", "417", "1"], ["40", "67c03791a55e5dd650be5ccd", "0xproject/0x-starter-project", "attrition", "2019-12-06", "dekz", "99", "1", "False", "465.0", "605.0", "0.4766355140186916", "51.0", "483", "1"], ["41", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "100", "1", "False", null, "50.0", "0.0313930673642903", "48.0", "620", "1"], ["42", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2023-03-08", "irisdv", "101", "1", "False", "229.0", "88.0", "0.0222367560497056", "34.0", "653", "1"], ["43", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2023-12-07", "janek26", "102", "1", "False", "274.0", "778.0", "0.1366906474820144", "209.0", "692", "1"], ["44", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2021-01-24", "0xsky", "103", "1", "False", null, "2392.0", "0.6623376623376623", "153.0", "542", "1"], ["45", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2022-03-22", "ox<PERSON>", "104", "1", "False", "422.0", "1253.0", "0.2251082251082251", "52.0", "603", "1"], ["46", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2023-06-13", "0xsky", "105", "1", "False", "448.0", "3262.0", "0.6666666666666666", "154.0", "667", "1"], ["47", "67c03791a55e5dd650be5cd2", "0xspaceshard/starknet-hardhat-plugin", "attrition", "2022-03-17", "dribeiro-Shard<PERSON>abs", "106", "1", "False", null, "104.0", "0.0857908847184986", "32.0", "602", "1"], ["48", "67c03791a55e5dd650be5cd3", "1-liners/1-liners", "attrition", "2015-07-05", "s<PERSON><PERSON><PERSON><PERSON>", "107", "1", "False", null, "76.0", "0.2868852459016393", "105.0", "252", "1"], ["49", "67c03791a55e5dd650be5cd6", "101loop/drf-user", "attrition", "2019-04-02", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "108", "1", "False", null, "636.0", "0.40625", "52.0", "448", "1"]], "shape": {"columns": 14, "rows": 91248}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_id</th>\n", "      <th>repo_name</th>\n", "      <th>attrition</th>\n", "      <th>attrition_date</th>\n", "      <th>attrition_developer</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>standardized_time_weeks</th>\n", "      <th>someone_left</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67c03791a55e5dd650be5caa</td>\n", "      <td>01mf02/jaq</td>\n", "      <td>attrition</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>854.0</td>\n", "      <td>0.155844</td>\n", "      <td>228.0</td>\n", "      <td>658</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>2189.0</td>\n", "      <td>0.548485</td>\n", "      <td>181.0</td>\n", "      <td>525</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "      <td>2592.0</td>\n", "      <td>0.551515</td>\n", "      <td>182.0</td>\n", "      <td>583</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>299.0</td>\n", "      <td>0.013271</td>\n", "      <td>93.0</td>\n", "      <td>502</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "      <td>581.0</td>\n", "      <td>0.020405</td>\n", "      <td>143.0</td>\n", "      <td>590</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91243</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "      <td>79.0</td>\n", "      <td>0.063158</td>\n", "      <td>12.0</td>\n", "      <td>415</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91244</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "      <td>1806.0</td>\n", "      <td>0.210526</td>\n", "      <td>40.0</td>\n", "      <td>632</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91245</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>27.0</td>\n", "      <td>0.016432</td>\n", "      <td>7.0</td>\n", "      <td>361</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91246</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "      <td>87.0</td>\n", "      <td>0.023474</td>\n", "      <td>10.0</td>\n", "      <td>427</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91247</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "      <td>820.0</td>\n", "      <td>0.018779</td>\n", "      <td>8.0</td>\n", "      <td>474</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>91248 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                        repo_id                                   repo_name  \\\n", "0      67c03791a55e5dd650be5caa                                  01mf02/jaq   \n", "1      67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "2      67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "3      67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "4      67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "...                         ...                                         ...   \n", "91243  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "91244  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "91245  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "91246  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "91247  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "\n", "       attrition attrition_date  attrition_developer   burst  attrition_count  \\\n", "0      attrition     2023-04-11  kammerchorinnsbruck       1                1   \n", "1      attrition     2020-09-25              rudyryk       2                1   \n", "2      attrition     2021-11-02              rudyryk       3                1   \n", "3      attrition     2020-04-14             bothra90       4                1   \n", "4      attrition     2021-12-21          gregnazario      13                1   \n", "...          ...            ...                  ...     ...              ...   \n", "91243  attrition     2018-08-13         stgelaisalex  157916                1   \n", "91244  attrition     2022-10-13       <PERSON>Magnan  157917                1   \n", "91245  attrition     2017-07-31             waqasm78  157918                1   \n", "91246  attrition     2018-11-09         stgelaisalex  157919                1   \n", "91247  attrition     2019-10-02             waqasm78  157920                1   \n", "\n", "       gap_less_than_84  inter_burst_gap  tenure  commit_percent  commits  \\\n", "0                 False              NaN   854.0        0.155844    228.0   \n", "1                 False              NaN  2189.0        0.548485    181.0   \n", "2                 False            403.0  2592.0        0.551515    182.0   \n", "3                 False              NaN   299.0        0.013271     93.0   \n", "4                 False            397.0   581.0        0.020405    143.0   \n", "...                 ...              ...     ...             ...      ...   \n", "91243             False            384.0    79.0        0.063158     12.0   \n", "91244             False           1522.0  1806.0        0.210526     40.0   \n", "91245             False              NaN    27.0        0.016432      7.0   \n", "91246             False            466.0    87.0        0.023474     10.0   \n", "91247             False            327.0   820.0        0.018779      8.0   \n", "\n", "       standardized_time_weeks  someone_left  \n", "0                          658             1  \n", "1                          525             1  \n", "2                          583             1  \n", "3                          502             1  \n", "4                          590             1  \n", "...                        ...           ...  \n", "91243                      415             1  \n", "91244                      632             1  \n", "91245                      361             1  \n", "91246                      427             1  \n", "91247                      474             1  \n", "\n", "[91248 rows x 14 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition_with_burst_original"]}, {"cell_type": "code", "execution_count": 25, "id": "6889cd19", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_id", "rawType": "int64", "type": "integer"}, {"name": "attrition_time", "rawType": "object", "type": "string"}, {"name": "dev_login", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}], "ref": "16208360-1609-4ed9-828d-1df359822ddb", "rows": [["0", "01mf02/jaq", "1", "2023-04-11", "kammerchorinnsbruck", "2023-04-11", "1", "1", "False", null], ["1", "05bit/peewee-async", "1", "2020-09-25", "rudyryk", "2020-09-25", "2", "1", "False", null], ["2", "05bit/peewee-async", "2", "2021-11-02", "rudyryk", "2021-11-02", "3", "1", "False", "403.0"], ["3", "0LNetworkCommunity/libra-legacy-v6", "1", "2020-04-14", "bothra90", "2020-04-14", "4", "1", "False", null], ["4", "0LNetworkCommunity/libra-legacy-v6", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "13", "1", "False", "397.0"], ["5", "0b01/tectonicdb", "1", "2020-09-08", "0b01", "2020-09-08", "14", "1", "False", null], ["6", "0b01/tectonicdb", "2", "2021-09-27", "0b01", "2021-09-27", "15", "1", "False", "384.0"], ["7", "0b01001001/spectree", "1", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "2023-03-22", "16", "1", "False", null], ["8", "0chain/0chain", "1", "2018-12-08", "sachin-0chain", "2018-12-08", "17", "1", "False", null], ["9", "0chain/0chain", "4", "2020-11-20", "IntegralTeam", "2020-11-20", "20", "1", "False", "478.0"], ["10", "0ldsk00l/nestopia", "1", "2007-02-20", "rdanbrook", "2007-02-20", "27", "1", "False", null], ["11", "0lnetworkcommunity/libra", "1", "2020-04-14", "bothra90", "2020-04-14", "28", "1", "False", null], ["12", "0lnetworkcommunity/libra", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "37", "1", "False", "397.0"], ["13", "0rpc/zerorpc-python", "1", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "2012-04-10", "40", "1", "False", null], ["14", "0rpc/zerorpc-python", "2", "2013-03-08", "lopter", "2013-03-08", "41", "1", "False", "332.0"], ["15", "0rpc/zerorpc-python", "3", "2014-03-07", "jpetazzo", "2014-03-07", "42", "1", "False", "364.0"], ["16", "0rpc/zerorpc-python", "4", "2018-02-02", "bombela", "2018-02-02", "43", "1", "False", "1428.0"], ["17", "0rpc/zerorpc-python", "5", "2019-06-26", "bombela", "2019-06-26", "44", "1", "False", "509.0"], ["18", "0x5bfa/FluentHub", "1", "2023-04-06", "Lamparter", "2023-04-06", "45", "1", "False", null], ["19", "0x7c13/notepads", "1", "2020-05-31", "Daxxxis", "2020-05-31", "46", "1", "False", null], ["20", "0xPolygonZero/plonky2", "1", "2023-05-16", "typ3c4t", "2023-05-16", "47", "1", "False", null], ["21", "0xax/linux-insides", "6", "2017-01-11", "<PERSON><PERSON><PERSON>g", "2017-01-11", "53", "1", "False", "344.0"], ["22", "0xax/linux-insides", "7", "2018-03-22", "<PERSON><PERSON><PERSON>g", "2018-03-22", "54", "1", "False", "435.0"], ["23", "0xax/linux-insides", "8", "2019-03-08", "0xAX", "2019-03-08", "55", "1", "False", "351.0"], ["24", "0xax/linux-insides", "9", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-11-10", "56", "1", "False", "247.0"], ["25", "0xax/linux-insides", "10", "2020-05-31", "0xAX", "2020-05-31", "57", "1", "False", "203.0"], ["26", "0xax/linux-insides", "14", "2022-08-03", "0xAX", "2022-07-31", "61", "2", "False", "591.0"], ["27", "0xax/linux-insides", "16", "2023-01-09", "<PERSON><PERSON><PERSON>g", "2023-01-09", "62", "1", "False", "159.0"], ["28", "0xfe/vextab", "1", "2014-11-28", "0xfe", "2014-11-28", "63", "1", "False", null], ["29", "0xfe/vextab", "2", "2016-07-10", "0xfe", "2016-07-10", "64", "1", "False", "590.0"], ["30", "0xfe/vextab", "3", "2017-11-29", "0xfe", "2017-11-29", "65", "1", "False", "507.0"], ["31", "0xfe/vextab", "4", "2019-02-08", "0xfe", "2019-02-08", "66", "1", "False", "436.0"], ["32", "0xpolygon/polygon-edge", "1", "2019-11-15", "ferranbt", "2019-11-15", "67", "1", "False", null], ["33", "0xpolygon/polygon-edge", "2", "2021-05-07", "ferranbt", "2021-05-07", "68", "1", "False", "539.0"], ["34", "0xpolygon/polygon-edge", "3", "2022-03-31", "munna0908", "2022-03-31", "69", "1", "False", "328.0"], ["35", "0xpolygon/polygon-sdk", "1", "2019-11-15", "ferranbt", "2019-11-15", "75", "1", "False", null], ["36", "0xpolygon/polygon-sdk", "2", "2021-05-07", "ferranbt", "2021-05-07", "76", "1", "False", "539.0"], ["37", "0xpolygon/polygon-sdk", "3", "2022-03-31", "munna0908", "2022-03-31", "77", "1", "False", "328.0"], ["38", "0xpolygonhermez/zkevm-node", "1", "2023-10-03", "Psykepro", "2023-10-03", "83", "1", "False", null], ["39", "0xpolygonmiden/crypto", "1", "2023-04-05", "vlopes11", "2023-04-05", "84", "1", "False", null], ["40", "0xproject/0x-starter-project", "1", "2018-08-28", "fabioberger", "2018-08-28", "98", "1", "False", null], ["41", "0xproject/0x-starter-project", "2", "2019-12-06", "dekz", "2019-12-06", "99", "1", "False", "465.0"], ["42", "0xs34n/starknet.js", "1", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "2022-07-22", "100", "1", "False", null], ["43", "0xs34n/starknet.js", "2", "2023-03-08", "irisdv", "2023-03-08", "101", "1", "False", "229.0"], ["44", "0xs34n/starknet.js", "3", "2023-12-07", "janek26", "2023-12-07", "102", "1", "False", "274.0"], ["45", "0xsky/xredis", "1", "2021-01-24", "0xsky", "2021-01-24", "103", "1", "False", null], ["46", "0xsky/xredis", "2", "2022-03-22", "ox<PERSON>", "2022-03-22", "104", "1", "False", "422.0"], ["47", "0xsky/xredis", "3", "2023-06-13", "0xsky", "2023-06-13", "105", "1", "False", "448.0"], ["48", "0xspaceshard/starknet-hardhat-plugin", "1", "2022-03-17", "dribeiro-Shard<PERSON>abs", "2022-03-17", "106", "1", "False", null], ["49", "1-liners/1-liners", "1", "2015-07-05", "s<PERSON><PERSON><PERSON><PERSON>", "2015-07-05", "107", "1", "False", null]], "shape": {"columns": 9, "rows": 93231}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_id</th>\n", "      <th>attrition_time</th>\n", "      <th>dev_login</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>1</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>2023-04-11</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>1</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2020-09-25</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>2</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2021-11-02</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>1</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>2020-04-14</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>21</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>2021-12-21</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93226</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>2</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-08-13</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93227</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>3</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>2022-10-13</td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93228</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>1</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>2017-07-31</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93229</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>2</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-11-09</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93230</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>2019-10-02</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>93231 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                                        repo_name  attrition_id  \\\n", "0                                      01mf02/jaq             1   \n", "1                              05bit/peewee-async             1   \n", "2                              05bit/peewee-async             2   \n", "3              0LNetworkCommunity/libra-legacy-v6             1   \n", "4              0LNetworkCommunity/libra-legacy-v6            21   \n", "...                                           ...           ...   \n", "93226  zzzprojects/entityframework.dynamicfilters             2   \n", "93227  zzzprojects/entityframework.dynamicfilters             3   \n", "93228               zzzprojects/html-agility-pack             1   \n", "93229               zzzprojects/html-agility-pack             2   \n", "93230               zzzprojects/html-agility-pack             3   \n", "\n", "      attrition_time            dev_login attrition_date   burst  \\\n", "0         2023-04-11  kammerchorinnsbruck     2023-04-11       1   \n", "1         2020-09-25              rudyryk     2020-09-25       2   \n", "2         2021-11-02              rudyryk     2021-11-02       3   \n", "3         2020-04-14             bothra90     2020-04-14       4   \n", "4         2021-12-21          gregnazario     2021-12-21      13   \n", "...              ...                  ...            ...     ...   \n", "93226     2018-08-13         stgelaisalex     2018-08-13  157916   \n", "93227     2022-10-13       JonathanMagnan     2022-10-13  157917   \n", "93228     2017-07-31             waqasm78     2017-07-31  157918   \n", "93229     2018-11-09         stgelaisalex     2018-11-09  157919   \n", "93230     2019-10-02             waqasm78     2019-10-02  157920   \n", "\n", "       attrition_count  gap_less_than_84  inter_burst_gap  \n", "0                    1             False              NaN  \n", "1                    1             False              NaN  \n", "2                    1             False            403.0  \n", "3                    1             False              NaN  \n", "4                    1             False            397.0  \n", "...                ...               ...              ...  \n", "93226                1             False            384.0  \n", "93227                1             False           1522.0  \n", "93228                1             False              NaN  \n", "93229                1             False            466.0  \n", "93230                1             False            327.0  \n", "\n", "[93231 rows x 9 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition_with_burst_new"]}, {"cell_type": "code", "execution_count": 21, "id": "394b252d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns used for comparison: ['repo_name', 'attrition_date', 'attrition_developer', 'burst']\n", "Rows only in original (not in new): 2080\n", "Sample rows only in original:\n", "                       repo_name attrition_date attrition_developer  burst\n", "27            0xax/linux-insides     2022-08-03                0xAX     61\n", "88   10up/restricted-site-access     2023-04-11       peterwilsoncc    158\n", "235        2ndquadrant-it/barman     2020-11-03          g<PERSON><PERSON><PERSON>    466\n", "383     BuildOnViction/tomochain     2021-09-13         ng<PERSON><PERSON><PERSON>am    829\n", "391  BuildOnViction/victionchain     2021-09-13         nguyenbatam    838\n", "Rows only in new (not in original): 4063\n", "Sample rows only in new:\n", "                      repo_name attrition_date attrition_developer  burst\n", "10            0ldsk00l/nestopia     2007-02-20           rdanbrook     27\n", "26           0xax/linux-insides     2022-07-31                0xAX     61\n", "52                  1024pix/pix     2019-07-21      la<PERSON>-<PERSON><PERSON><PERSON>    109\n", "84  10up/restricted-site-access     2018-04-25             d<PERSON><PERSON>    155\n", "87  10up/restricted-site-access     2023-04-04       pet<PERSON><PERSON><PERSON><PERSON>    158\n"]}], "source": ["# Compare the difference between attrition_with_burst_new and attrition_with_burst_original\n", "# by repo_name, attrition_date, attrition_developer, burst\n", "\n", "# First, ensure the relevant columns exist and are named consistently\n", "# Standardize column names for comparison\n", "def standardize_columns(df):\n", "    df = df.copy()\n", "    # Try to find the correct column names for attrition date and developer\n", "    # Try common variants\n", "    if 'attrition_date' not in df.columns:\n", "        for col in df.columns:\n", "            if 'date' in col and 'attrition' in col:\n", "                df = df.rename(columns={col: 'attrition_date'})\n", "    if 'attrition_developer' not in df.columns:\n", "        # The new file uses 'attrition_develper' (typo), fix it\n", "        if 'attrition_develper' in df.columns:\n", "            df = df.rename(columns={'attrition_develper': 'attrition_developer'})\n", "        else:\n", "            for col in df.columns:\n", "                if 'dev' in col and 'attrition' in col:\n", "                    df = df.rename(columns={col: 'attrition_developer'})\n", "    return df\n", "\n", "attrition_with_burst_new_std = standardize_columns(attrition_with_burst_new)\n", "attrition_with_burst_original_std = standardize_columns(attrition_with_burst_original)\n", "\n", "# Select only the relevant columns for comparison\n", "compare_cols = ['repo_name', 'attrition_date', 'attrition_developer', 'burst']\n", "\n", "# Check which columns are present in both\n", "present_cols = [col for col in compare_cols if col in attrition_with_burst_new_std.columns and col in attrition_with_burst_original_std.columns]\n", "\n", "print(\"Columns used for comparison:\", present_cols)\n", "\n", "# Subset the dataframes\n", "df_new = attrition_with_burst_new_std[present_cols].drop_duplicates().reset_index(drop=True)\n", "df_org = attrition_with_burst_original_std[present_cols].drop_duplicates().reset_index(drop=True)\n", "\n", "# Merge to find differences\n", "merged = df_org.merge(df_new, on=present_cols, how='outer', indicator=True)\n", "\n", "only_in_org = merged[merged['_merge'] == 'left_only']\n", "only_in_new = merged[merged['_merge'] == 'right_only']\n", "\n", "print(f\"Rows only in original (not in new): {only_in_org.shape[0]}\")\n", "if only_in_org.shape[0] > 0:\n", "    print(\"Sample rows only in original:\")\n", "    print(only_in_org[present_cols].head())\n", "\n", "print(f\"Rows only in new (not in original): {only_in_new.shape[0]}\")\n", "if only_in_new.shape[0] > 0:\n", "    print(\"Sample rows only in new:\")\n", "    print(only_in_new[present_cols].head())\n"]}, {"cell_type": "code", "execution_count": 26, "id": "2fe72696", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_id", "rawType": "object", "type": "string"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}], "ref": "93bbb3bf-03fd-4844-9648-bab7fe16e10b", "rows": [["0", "67c03791a55e5dd650be5caa", "01mf02/jaq", "attrition", "2023-04-11", "kammerchorinnsbruck", "1", "1", "False", null], ["1", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2020-09-25", "rudyryk", "2", "1", "False", null], ["2", "67c03791a55e5dd650be5cac", "05bit/peewee-async", "attrition", "2021-11-02", "rudyryk", "3", "1", "False", "403.0"], ["3", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2020-04-14", "bothra90", "4", "1", "False", null], ["4", "67c03791a55e5dd650be5cad", "0LNetworkCommunity/libra-legacy-v6", "attrition", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "13", "1", "False", "397.0"], ["5", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2020-09-08", "0b01", "14", "1", "False", null], ["6", "67c03791a55e5dd650be5caf", "0b01/tectonicdb", "attrition", "2021-09-27", "0b01", "15", "1", "False", "384.0"], ["7", "67c03791a55e5dd650be5cb0", "0b01001001/spectree", "attrition", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "16", "1", "False", null], ["8", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2018-12-08", "sachin-0chain", "17", "1", "False", null], ["9", "67c03791a55e5dd650be5cb1", "0chain/0chain", "attrition", "2020-11-20", "IntegralTeam", "20", "1", "False", "478.0"], ["10", "67c03791a55e5dd650be5cb2", "0ldsk00l/nestopia", "attrition", "2007-02-20", "rdanbrook", "27", "1", "False", null], ["11", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2020-04-14", "bothra90", "28", "1", "False", null], ["12", "67c03791a55e5dd650be5cb3", "0lnetworkcommunity/libra", "attrition", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "37", "1", "False", "397.0"], ["13", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "40", "1", "False", null], ["14", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2013-03-08", "lopter", "41", "1", "False", "332.0"], ["15", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2014-03-07", "jpetazzo", "42", "1", "False", "364.0"], ["16", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2018-02-02", "bombela", "43", "1", "False", "1428.0"], ["17", "67c03791a55e5dd650be5cb7", "0rpc/zerorpc-python", "attrition", "2019-06-26", "bombela", "44", "1", "False", "509.0"], ["18", "67c03791a55e5dd650be5cb9", "0x5bfa/FluentHub", "attrition", "2023-04-06", "Lamparter", "45", "1", "False", null], ["19", "67c03791a55e5dd650be5cba", "0x7c13/notepads", "attrition", "2020-05-31", "Daxxxis", "46", "1", "False", null], ["20", "67c03791a55e5dd650be5cbc", "0xPolygonZero/plonky2", "attrition", "2023-05-16", "typ3c4t", "47", "1", "False", null], ["21", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2017-01-11", "<PERSON><PERSON><PERSON>g", "53", "1", "False", "344.0"], ["22", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2018-03-22", "<PERSON><PERSON><PERSON>g", "54", "1", "False", "435.0"], ["23", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2019-03-08", "0xAX", "55", "1", "False", "351.0"], ["24", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "56", "1", "False", "247.0"], ["25", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2020-05-31", "0xAX", "57", "1", "False", "203.0"], ["26", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2022-07-31", "0xAX", "61", "2", "False", "591.0"], ["27", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2022-08-03", "rena<PERSON><PERSON><PERSON>", "61", "2", "False", "591.0"], ["28", "67c03791a55e5dd650be5cbd", "0xax/linux-insides", "attrition", "2023-01-09", "<PERSON><PERSON><PERSON>g", "62", "1", "False", "159.0"], ["29", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2014-11-28", "0xfe", "63", "1", "False", null], ["30", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2016-07-10", "0xfe", "64", "1", "False", "590.0"], ["31", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2017-11-29", "0xfe", "65", "1", "False", "507.0"], ["32", "67c03791a55e5dd650be5cc1", "0xfe/vextab", "attrition", "2019-02-08", "0xfe", "66", "1", "False", "436.0"], ["33", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2019-11-15", "ferranbt", "67", "1", "False", null], ["34", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2021-05-07", "ferranbt", "68", "1", "False", "539.0"], ["35", "67c03791a55e5dd650be5cc3", "0xpolygon/polygon-edge", "attrition", "2022-03-31", "munna0908", "69", "1", "False", "328.0"], ["36", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2019-11-15", "ferranbt", "75", "1", "False", null], ["37", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2021-05-07", "ferranbt", "76", "1", "False", "539.0"], ["38", "67c03791a55e5dd650be5cc4", "0xpolygon/polygon-sdk", "attrition", "2022-03-31", "munna0908", "77", "1", "False", "328.0"], ["39", "67c03791a55e5dd650be5cc6", "0xpolygonhermez/zkevm-node", "attrition", "2023-10-03", "Psykepro", "83", "1", "False", null], ["40", "67c03791a55e5dd650be5cc8", "0xpolygonmiden/crypto", "attrition", "2023-04-05", "vlopes11", "84", "1", "False", null], ["41", "67c03791a55e5dd650be5ccd", "0xproject/0x-starter-project", "attrition", "2018-08-28", "fabioberger", "98", "1", "False", null], ["42", "67c03791a55e5dd650be5ccd", "0xproject/0x-starter-project", "attrition", "2019-12-06", "dekz", "99", "1", "False", "465.0"], ["43", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "100", "1", "False", null], ["44", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2023-03-08", "irisdv", "101", "1", "False", "229.0"], ["45", "67c03791a55e5dd650be5cce", "0xs34n/starknet.js", "attrition", "2023-12-07", "janek26", "102", "1", "False", "274.0"], ["46", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2021-01-24", "0xsky", "103", "1", "False", null], ["47", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2022-03-22", "ox<PERSON>", "104", "1", "False", "422.0"], ["48", "67c03791a55e5dd650be5cd0", "0xsky/xredis", "attrition", "2023-06-13", "0xsky", "105", "1", "False", "448.0"], ["49", "67c03791a55e5dd650be5cd2", "0xspaceshard/starknet-hardhat-plugin", "attrition", "2022-03-17", "dribeiro-Shard<PERSON>abs", "106", "1", "False", null]], "shape": {"columns": 9, "rows": 95958}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_id</th>\n", "      <th>repo_name</th>\n", "      <th>attrition</th>\n", "      <th>attrition_date</th>\n", "      <th>attrition_developer</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>67c03791a55e5dd650be5caa</td>\n", "      <td>01mf02/jaq</td>\n", "      <td>attrition</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>67c03791a55e5dd650be5cac</td>\n", "      <td>05bit/peewee-async</td>\n", "      <td>attrition</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>67c03791a55e5dd650be5cad</td>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>attrition</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95953</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95954</th>\n", "      <td>67c0379ca55e5dd650bf13b4</td>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>attrition</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95955</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95956</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95957</th>\n", "      <td>67c0379ca55e5dd650bf13b5</td>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>attrition</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>95958 rows × 9 columns</p>\n", "</div>"], "text/plain": ["                        repo_id                                   repo_name  \\\n", "0      67c03791a55e5dd650be5caa                                  01mf02/jaq   \n", "1      67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "2      67c03791a55e5dd650be5cac                          05bit/peewee-async   \n", "3      67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "4      67c03791a55e5dd650be5cad          0LNetworkCommunity/libra-legacy-v6   \n", "...                         ...                                         ...   \n", "95953  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "95954  67c0379ca55e5dd650bf13b4  zzzprojects/entityframework.dynamicfilters   \n", "95955  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "95956  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "95957  67c0379ca55e5dd650bf13b5               zzzprojects/html-agility-pack   \n", "\n", "       attrition attrition_date  attrition_developer   burst  attrition_count  \\\n", "0      attrition     2023-04-11  kammerchorinnsbruck       1                1   \n", "1      attrition     2020-09-25              rudyryk       2                1   \n", "2      attrition     2021-11-02              rudyryk       3                1   \n", "3      attrition     2020-04-14             bothra90       4                1   \n", "4      attrition     2021-12-21          gregnazario      13                1   \n", "...          ...            ...                  ...     ...              ...   \n", "95953  attrition     2018-08-13         stgelaisalex  157916                1   \n", "95954  attrition     2022-10-13       <PERSON>  157917                1   \n", "95955  attrition     2017-07-31             waqasm78  157918                1   \n", "95956  attrition     2018-11-09         stgelaisalex  157919                1   \n", "95957  attrition     2019-10-02             waqasm78  157920                1   \n", "\n", "       gap_less_than_84  inter_burst_gap  \n", "0                 False              NaN  \n", "1                 False              NaN  \n", "2                 False            403.0  \n", "3                 False              NaN  \n", "4                 False            397.0  \n", "...                 ...              ...  \n", "95953             False            384.0  \n", "95954             False           1522.0  \n", "95955             False              NaN  \n", "95956             False            466.0  \n", "95957             False            327.0  \n", "\n", "[95958 rows x 9 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition_with_burst_original_without_merge"]}, {"cell_type": "code", "execution_count": 27, "id": "d1a90e39", "metadata": {}, "outputs": [], "source": ["attrition_with_burst_without_merge = attrition_with_burst_without_merge[attrition_with_burst_without_merge['gap_less_than_84'] == False]"]}, {"cell_type": "code", "execution_count": 28, "id": "3f8324c9", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_id", "rawType": "int64", "type": "integer"}, {"name": "attrition_time", "rawType": "object", "type": "string"}, {"name": "dev_login", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "int64", "type": "integer"}], "ref": "c5180744-ab39-4376-be0e-1fbba906b660", "rows": [["0", "01mf02/jaq", "1", "2023-04-11", "kammerchorinnsbruck", "2023-04-11", "1", "1", "False", null, "1", "854.0", "0.1558441558441558", "228"], ["1", "05bit/peewee-async", "1", "2020-09-25", "rudyryk", "2020-09-25", "2", "1", "False", null, "1", "2189.0", "0.5484848484848485", "181"], ["2", "05bit/peewee-async", "2", "2021-11-02", "rudyryk", "2021-11-02", "3", "1", "False", "403.0", "1", "2592.0", "0.5515151515151515", "182"], ["3", "0LNetworkCommunity/libra-legacy-v6", "1", "2020-04-14", "bothra90", "2020-04-14", "4", "1", "False", null, "1", "299.0", "0.0132705479452054", "93"], ["23", "0LNetworkCommunity/libra-legacy-v6", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "13", "1", "False", "397.0", "1", "581.0", "0.0204052511415525", "143"], ["24", "0b01/tectonicdb", "1", "2020-09-08", "0b01", "2020-09-08", "14", "1", "False", null, "1", "1070.0", "0.8469827586206896", "393"], ["25", "0b01/tectonicdb", "2", "2021-09-27", "0b01", "2021-09-27", "15", "1", "False", "384.0", "1", "1454.0", "0.8491379310344828", "394"], ["26", "0b01001001/spectree", "1", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "2023-03-22", "16", "1", "False", null, "1", "331.0", "0.0308483290488431", "12"], ["27", "0chain/0chain", "1", "2018-12-08", "sachin-0chain", "2018-12-08", "17", "1", "False", null, "1", "159.0", "0.0215907286870931", "272"], ["30", "0chain/0chain", "4", "2020-11-20", "IntegralTeam", "2020-11-20", "20", "1", "False", "478.0", "1", "288.0", "0.0536593110017463", "676"], ["38", "0ldsk00l/nestopia", "1", "2007-02-20", "rdanbrook", "2007-02-20", "27", "1", "False", null, "1", null, "0.0", "0"], ["39", "0lnetworkcommunity/libra", "1", "2020-04-14", "bothra90", "2020-04-14", "28", "1", "False", null, "1", "299.0", "0.0143055555555555", "103"], ["59", "0lnetworkcommunity/libra", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "37", "1", "False", "397.0", "1", "581.0", "0.02", "144"], ["62", "0rpc/zerorpc-python", "1", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "2012-04-10", "40", "1", "False", null, "1", "30.0", "0.038647342995169", "8"], ["63", "0rpc/zerorpc-python", "2", "2013-03-08", "lopter", "2013-03-08", "41", "1", "False", "332.0", "1", "338.0", "0.0434782608695652", "9"], ["64", "0rpc/zerorpc-python", "3", "2014-03-07", "jpetazzo", "2014-03-07", "42", "1", "False", "364.0", "1", "727.0", "0.0289855072463768", "6"], ["65", "0rpc/zerorpc-python", "4", "2018-02-02", "bombela", "2018-02-02", "43", "1", "False", "1428.0", "1", "2168.0", "0.5797101449275363", "120"], ["66", "0rpc/zerorpc-python", "5", "2019-06-26", "bombela", "2019-06-26", "44", "1", "False", "509.0", "1", "2677.0", "0.5845410628019324", "121"], ["67", "0x5bfa/FluentHub", "1", "2023-04-06", "Lamparter", "2023-04-06", "45", "1", "False", null, "1", "319.0", "0.0492813141683778", "24"], ["68", "0x7c13/notepads", "1", "2020-05-31", "Daxxxis", "2020-05-31", "46", "1", "False", null, "1", "83.0", "0.014069264069264", "13"], ["69", "0xPolygonZero/plonky2", "1", "2023-05-16", "typ3c4t", "2023-05-16", "47", "1", "False", null, "1", "292.0", "0.1144607843137254", "467"], ["75", "0xax/linux-insides", "6", "2017-01-11", "<PERSON><PERSON><PERSON>g", "2017-01-11", "53", "1", "False", "344.0", "1", "531.0", "0.0230891719745222", "29"], ["76", "0xax/linux-insides", "7", "2018-03-22", "<PERSON><PERSON><PERSON>g", "2018-03-22", "54", "1", "False", "435.0", "1", "966.0", "0.0302547770700636", "38"], ["77", "0xax/linux-insides", "8", "2019-03-08", "0xAX", "2019-03-08", "55", "1", "False", "351.0", "1", "1524.0", "0.3805732484076433", "478"], ["78", "0xax/linux-insides", "9", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-11-10", "56", "1", "False", "247.0", "1", "1036.0", "0.0063694267515923", "8"], ["79", "0xax/linux-insides", "10", "2020-05-31", "0xAX", "2020-05-31", "57", "1", "False", "203.0", "1", "1974.0", "0.3813694267515923", "479"], ["83", "0xax/linux-insides", "14", "2022-07-31", "0xAX", "2022-07-31", "61", "2", "False", "591.0", "1", "2765.0", "0.3821656050955414", "480"], ["84", "0xax/linux-insides", "15", "2022-08-03", "rena<PERSON><PERSON><PERSON>", "2022-08-03", "61", "2", "False", "591.0", "1", "144.0", "0.0103503184713375", "13"], ["85", "0xax/linux-insides", "16", "2023-01-09", "<PERSON><PERSON><PERSON>g", "2023-01-09", "62", "1", "False", "159.0", "1", "2720.0", "0.0326433121019108", "41"], ["86", "0xfe/vextab", "1", "2014-11-28", "0xfe", "2014-11-28", "63", "1", "False", null, "1", "719.0", "0.6614173228346457", "168"], ["87", "0xfe/vextab", "2", "2016-07-10", "0xfe", "2016-07-10", "64", "1", "False", "590.0", "1", "1309.0", "0.7716535433070866", "196"], ["88", "0xfe/vextab", "3", "2017-11-29", "0xfe", "2017-11-29", "65", "1", "False", "507.0", "1", "1816.0", "0.8031496062992126", "204"], ["89", "0xfe/vextab", "4", "2019-02-08", "0xfe", "2019-02-08", "66", "1", "False", "436.0", "1", "2252.0", "0.8070866141732284", "205"], ["90", "0xpolygon/polygon-edge", "1", "2019-11-15", "ferranbt", "2019-11-15", "67", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["91", "0xpolygon/polygon-edge", "2", "2021-05-07", "ferranbt", "2021-05-07", "68", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["92", "0xpolygon/polygon-edge", "3", "2022-03-31", "munna0908", "2022-03-31", "69", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["99", "0xpolygon/polygon-sdk", "1", "2019-11-15", "ferranbt", "2019-11-15", "75", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["100", "0xpolygon/polygon-sdk", "2", "2021-05-07", "ferranbt", "2021-05-07", "76", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["101", "0xpolygon/polygon-sdk", "3", "2022-03-31", "munna0908", "2022-03-31", "77", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["108", "0xpolygonhermez/zkevm-node", "1", "2023-10-03", "Psykepro", "2023-10-03", "83", "1", "False", null, "1", "347.0", "0.0675580577058409", "96"], ["109", "0xpolygonmiden/crypto", "1", "2023-04-05", "vlopes11", "2023-04-05", "84", "1", "False", null, "1", "166.0", "0.0728476821192053", "22"], ["125", "0xproject/0x-starter-project", "1", "2018-08-28", "fabioberger", "2018-08-28", "98", "1", "False", null, "1", "306.0", "0.102803738317757", "11"], ["126", "0xproject/0x-starter-project", "2", "2019-12-06", "dekz", "2019-12-06", "99", "1", "False", "465.0", "1", "605.0", "0.4766355140186916", "51"], ["127", "0xs34n/starknet.js", "1", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "2022-07-22", "100", "1", "False", null, "1", "50.0", "0.0313930673642903", "48"], ["128", "0xs34n/starknet.js", "2", "2023-03-08", "irisdv", "2023-03-08", "101", "1", "False", "229.0", "1", "88.0", "0.0222367560497056", "34"], ["129", "0xs34n/starknet.js", "3", "2023-12-07", "janek26", "2023-12-07", "102", "1", "False", "274.0", "1", "778.0", "0.1366906474820144", "209"], ["130", "0xsky/xredis", "1", "2021-01-24", "0xsky", "2021-01-24", "103", "1", "False", null, "1", "2392.0", "0.6623376623376623", "153"], ["131", "0xsky/xredis", "2", "2022-03-22", "ox<PERSON>", "2022-03-22", "104", "1", "False", "422.0", "1", "1253.0", "0.2251082251082251", "52"], ["132", "0xsky/xredis", "3", "2023-06-13", "0xsky", "2023-06-13", "105", "1", "False", "448.0", "1", "3262.0", "0.6666666666666666", "154"], ["133", "0xspaceshard/starknet-hardhat-plugin", "1", "2022-03-17", "dribeiro-Shard<PERSON>abs", "2022-03-17", "106", "1", "False", null, "1", "104.0", "0.0857908847184986", "32"]], "shape": {"columns": 13, "rows": 95958}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_id</th>\n", "      <th>attrition_time</th>\n", "      <th>dev_login</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>1</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>2023-04-11</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>854.0</td>\n", "      <td>0.155844</td>\n", "      <td>228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>1</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2020-09-25</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>2189.0</td>\n", "      <td>0.548485</td>\n", "      <td>181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>2</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2021-11-02</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "      <td>1</td>\n", "      <td>2592.0</td>\n", "      <td>0.551515</td>\n", "      <td>182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>1</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>2020-04-14</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>299.0</td>\n", "      <td>0.013271</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>21</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>2021-12-21</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "      <td>1</td>\n", "      <td>581.0</td>\n", "      <td>0.020405</td>\n", "      <td>143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174882</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>2</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-08-13</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "      <td>1</td>\n", "      <td>79.0</td>\n", "      <td>0.063158</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174883</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>3</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>2022-10-13</td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "      <td>1</td>\n", "      <td>1806.0</td>\n", "      <td>0.210526</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174884</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>1</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>2017-07-31</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>27.0</td>\n", "      <td>0.016432</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174885</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>2</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-11-09</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "      <td>1</td>\n", "      <td>87.0</td>\n", "      <td>0.023474</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174886</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>2019-10-02</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "      <td>1</td>\n", "      <td>820.0</td>\n", "      <td>0.018779</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>95958 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                         repo_name  attrition_id  \\\n", "0                                       01mf02/jaq             1   \n", "1                               05bit/peewee-async             1   \n", "2                               05bit/peewee-async             2   \n", "3               0LNetworkCommunity/libra-legacy-v6             1   \n", "23              0LNetworkCommunity/libra-legacy-v6            21   \n", "...                                            ...           ...   \n", "174882  zzzprojects/entityframework.dynamicfilters             2   \n", "174883  zzzprojects/entityframework.dynamicfilters             3   \n", "174884               zzzprojects/html-agility-pack             1   \n", "174885               zzzprojects/html-agility-pack             2   \n", "174886               zzzprojects/html-agility-pack             3   \n", "\n", "       attrition_time            dev_login attrition_date   burst  \\\n", "0          2023-04-11  kammerchorinnsbruck     2023-04-11       1   \n", "1          2020-09-25              rudyryk     2020-09-25       2   \n", "2          2021-11-02              rudyryk     2021-11-02       3   \n", "3          2020-04-14             bothra90     2020-04-14       4   \n", "23         2021-12-21          gregnazario     2021-12-21      13   \n", "...               ...                  ...            ...     ...   \n", "174882     2018-08-13         stgelaisalex     2018-08-13  157916   \n", "174883     2022-10-13       <PERSON><PERSON>agnan     2022-10-13  157917   \n", "174884     2017-07-31             waqasm78     2017-07-31  157918   \n", "174885     2018-11-09         stgelaisalex     2018-11-09  157919   \n", "174886     2019-10-02             waqasm78     2019-10-02  157920   \n", "\n", "        attrition_count  gap_less_than_84  inter_burst_gap  someone_left  \\\n", "0                     1             False              NaN             1   \n", "1                     1             False              NaN             1   \n", "2                     1             False            403.0             1   \n", "3                     1             False              NaN             1   \n", "23                    1             False            397.0             1   \n", "...                 ...               ...              ...           ...   \n", "174882                1             False            384.0             1   \n", "174883                1             False           1522.0             1   \n", "174884                1             False              NaN             1   \n", "174885                1             False            466.0             1   \n", "174886                1             False            327.0             1   \n", "\n", "        tenure  commit_percent  commits  \n", "0        854.0        0.155844      228  \n", "1       2189.0        0.548485      181  \n", "2       2592.0        0.551515      182  \n", "3        299.0        0.013271       93  \n", "23       581.0        0.020405      143  \n", "...        ...             ...      ...  \n", "174882    79.0        0.063158       12  \n", "174883  1806.0        0.210526       40  \n", "174884    27.0        0.016432        7  \n", "174885    87.0        0.023474       10  \n", "174886   820.0        0.018779        8  \n", "\n", "[95958 rows x 13 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["attrition_with_burst_without_merge"]}, {"cell_type": "code", "execution_count": 9, "id": "a6da1bf3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original without merge shape: (95958, 9)\n", "New without merge shape: (95958, 9)\n", "\n", "Columns only in original: {'attrition_developer', 'repo_id', 'attrition'}\n", "Columns only in new: {'dev_login', 'attrition_time', 'attrition_id'}\n", "\n", "Value counts for 'repo_name' in original:\n", "repo_name\n", "valvesoftware/wine          27\n", "iains/gcc-darwin-arm64      25\n", "cgal/cgal                   24\n", "riscv/riscv-binutils-gdb    24\n", "Rust-GCC/gccrs              22\n", "Name: count, dtype: int64\n", "Value counts for 'repo_name' in new:\n", "repo_name\n", "valvesoftware/wine          27\n", "iains/gcc-darwin-arm64      25\n", "cgal/cgal                   24\n", "riscv/riscv-binutils-gdb    24\n", "Rust-GCC/gccrs              22\n", "Name: count, dtype: int64\n", "\n", "Value counts for 'burst' in original:\n", "burst\n", "60418     8\n", "157208    6\n", "84523     6\n", "22921     5\n", "107060    4\n", "Name: count, dtype: int64\n", "Value counts for 'burst' in new:\n", "burst\n", "60418     8\n", "157208    6\n", "84523     6\n", "22921     5\n", "107060    4\n", "Name: count, dtype: int64\n", "\n", "Rows only in original (not in new): 0\n", "Rows only in new (not in original): 0\n"]}], "source": ["# Compare the differences between attrition_with_burst_original_without_merge and attrition_with_burst_without_merge\n", "\n", "# 1. Check shape differences\n", "print(\"Original without merge shape:\", attrition_with_burst_original_without_merge.shape)\n", "print(\"New without merge shape:\", attrition_with_burst_without_merge.shape)\n", "\n", "# 2. Check columns differences\n", "print(\"\\nColumns only in original:\", set(attrition_with_burst_original_without_merge.columns) - set(attrition_with_burst_without_merge.columns))\n", "print(\"Columns only in new:\", set(attrition_with_burst_without_merge.columns) - set(attrition_with_burst_original_without_merge.columns))\n", "\n", "# 3. Check for overlapping columns and compare value distributions for key columns\n", "common_cols = [col for col in attrition_with_burst_original_without_merge.columns if col in attrition_with_burst_without_merge.columns]\n", "\n", "# 4. Compare value counts for a key identifier (e.g., repo_name, attrition_id, burst)\n", "for col in ['repo_name', 'burst', 'attrition_id']:\n", "    if col in common_cols:\n", "        print(f\"\\nValue counts for '{col}' in original:\")\n", "        print(attrition_with_burst_original_without_merge[col].value_counts().head())\n", "        print(f\"Value counts for '{col}' in new:\")\n", "        print(attrition_with_burst_without_merge[col].value_counts().head())\n", "\n", "# 5. Find rows in original not in new and vice versa (using a subset of columns as key)\n", "key_cols = [col for col in ['repo_name', 'attrition_id', 'burst'] if col in common_cols]\n", "if key_cols:\n", "    merged = attrition_with_burst_original_without_merge.merge(\n", "        attrition_with_burst_without_merge,\n", "        on=key_cols,\n", "        how='outer',\n", "        indicator=True\n", "    )\n", "    only_in_original = merged[merged['_merge'] == 'left_only']\n", "    only_in_new = merged[merged['_merge'] == 'right_only']\n", "    print(f\"\\nRows only in original (not in new): {only_in_original.shape[0]}\")\n", "    print(f\"Rows only in new (not in original): {only_in_new.shape[0]}\")\n", "    if only_in_original.shape[0] > 0:\n", "        print(\"Sample rows only in original:\")\n", "        print(only_in_original.head())\n", "    if only_in_new.shape[0] > 0:\n", "        print(\"Sample rows only in new:\")\n", "        print(only_in_new.head())\n", "else:\n", "    print(\"\\nNo common key columns found for row-level comparison.\")\n"]}, {"cell_type": "code", "execution_count": 33, "id": "b01c8cf3", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_id", "rawType": "int64", "type": "integer"}, {"name": "attrition_time", "rawType": "object", "type": "string"}, {"name": "dev_login", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "int64", "type": "integer"}], "ref": "94e2ea51-a492-4bbe-b5ea-584af5d21738", "rows": [["0", "01mf02/jaq", "1", "2023-04-11", "kammerchorinnsbruck", "2023-04-11", "1", "1", "False", null, "1", "854.0", "0.1558441558441558", "228"], ["1", "05bit/peewee-async", "1", "2020-09-25", "rudyryk", "2020-09-25", "2", "1", "False", null, "1", "2189.0", "0.5484848484848485", "181"], ["2", "05bit/peewee-async", "2", "2021-11-02", "rudyryk", "2021-11-02", "3", "1", "False", "403.0", "1", "2592.0", "0.5515151515151515", "182"], ["3", "0LNetworkCommunity/libra-legacy-v6", "1", "2020-04-14", "bothra90", "2020-04-14", "4", "1", "False", null, "1", "299.0", "0.0132705479452054", "93"], ["23", "0LNetworkCommunity/libra-legacy-v6", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "13", "1", "False", "397.0", "1", "581.0", "0.0204052511415525", "143"], ["24", "0b01/tectonicdb", "1", "2020-09-08", "0b01", "2020-09-08", "14", "1", "False", null, "1", "1070.0", "0.8469827586206896", "393"], ["25", "0b01/tectonicdb", "2", "2021-09-27", "0b01", "2021-09-27", "15", "1", "False", "384.0", "1", "1454.0", "0.8491379310344828", "394"], ["26", "0b01001001/spectree", "1", "2023-03-22", "yed<PERSON><PERSON><PERSON><PERSON><PERSON>", "2023-03-22", "16", "1", "False", null, "1", "331.0", "0.0308483290488431", "12"], ["27", "0chain/0chain", "1", "2018-12-08", "sachin-0chain", "2018-12-08", "17", "1", "False", null, "1", "159.0", "0.0215907286870931", "272"], ["30", "0chain/0chain", "4", "2020-11-20", "IntegralTeam", "2020-11-20", "20", "1", "False", "478.0", "1", "288.0", "0.0536593110017463", "676"], ["39", "0lnetworkcommunity/libra", "1", "2020-04-14", "bothra90", "2020-04-14", "28", "1", "False", null, "1", "299.0", "0.0143055555555555", "103"], ["59", "0lnetworkcommunity/libra", "21", "2021-12-21", "gre<PERSON><PERSON><PERSON>", "2021-12-21", "37", "1", "False", "397.0", "1", "581.0", "0.02", "144"], ["62", "0rpc/zerorpc-python", "1", "2012-04-10", "<PERSON><PERSON><PERSON><PERSON>", "2012-04-10", "40", "1", "False", null, "1", "30.0", "0.038647342995169", "8"], ["63", "0rpc/zerorpc-python", "2", "2013-03-08", "lopter", "2013-03-08", "41", "1", "False", "332.0", "1", "338.0", "0.0434782608695652", "9"], ["64", "0rpc/zerorpc-python", "3", "2014-03-07", "jpetazzo", "2014-03-07", "42", "1", "False", "364.0", "1", "727.0", "0.0289855072463768", "6"], ["65", "0rpc/zerorpc-python", "4", "2018-02-02", "bombela", "2018-02-02", "43", "1", "False", "1428.0", "1", "2168.0", "0.5797101449275363", "120"], ["66", "0rpc/zerorpc-python", "5", "2019-06-26", "bombela", "2019-06-26", "44", "1", "False", "509.0", "1", "2677.0", "0.5845410628019324", "121"], ["67", "0x5bfa/FluentHub", "1", "2023-04-06", "Lamparter", "2023-04-06", "45", "1", "False", null, "1", "319.0", "0.0492813141683778", "24"], ["68", "0x7c13/notepads", "1", "2020-05-31", "Daxxxis", "2020-05-31", "46", "1", "False", null, "1", "83.0", "0.014069264069264", "13"], ["69", "0xPolygonZero/plonky2", "1", "2023-05-16", "typ3c4t", "2023-05-16", "47", "1", "False", null, "1", "292.0", "0.1144607843137254", "467"], ["75", "0xax/linux-insides", "6", "2017-01-11", "<PERSON><PERSON><PERSON>g", "2017-01-11", "53", "1", "False", "344.0", "1", "531.0", "0.0230891719745222", "29"], ["76", "0xax/linux-insides", "7", "2018-03-22", "<PERSON><PERSON><PERSON>g", "2018-03-22", "54", "1", "False", "435.0", "1", "966.0", "0.0302547770700636", "38"], ["77", "0xax/linux-insides", "8", "2019-03-08", "0xAX", "2019-03-08", "55", "1", "False", "351.0", "1", "1524.0", "0.3805732484076433", "478"], ["78", "0xax/linux-insides", "9", "2019-11-10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-11-10", "56", "1", "False", "247.0", "1", "1036.0", "0.0063694267515923", "8"], ["79", "0xax/linux-insides", "10", "2020-05-31", "0xAX", "2020-05-31", "57", "1", "False", "203.0", "1", "1974.0", "0.3813694267515923", "479"], ["83", "0xax/linux-insides", "14", "2022-07-31", "0xAX", "2022-08-03", "61", "2", "False", "591.0", "1", "1454.5", "0.3925159235668789", "493"], ["85", "0xax/linux-insides", "16", "2023-01-09", "<PERSON><PERSON><PERSON>g", "2023-01-09", "62", "1", "False", "159.0", "1", "2720.0", "0.0326433121019108", "41"], ["86", "0xfe/vextab", "1", "2014-11-28", "0xfe", "2014-11-28", "63", "1", "False", null, "1", "719.0", "0.6614173228346457", "168"], ["87", "0xfe/vextab", "2", "2016-07-10", "0xfe", "2016-07-10", "64", "1", "False", "590.0", "1", "1309.0", "0.7716535433070866", "196"], ["88", "0xfe/vextab", "3", "2017-11-29", "0xfe", "2017-11-29", "65", "1", "False", "507.0", "1", "1816.0", "0.8031496062992126", "204"], ["89", "0xfe/vextab", "4", "2019-02-08", "0xfe", "2019-02-08", "66", "1", "False", "436.0", "1", "2252.0", "0.8070866141732284", "205"], ["90", "0xpolygon/polygon-edge", "1", "2019-11-15", "ferranbt", "2019-11-15", "67", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["91", "0xpolygon/polygon-edge", "2", "2021-05-07", "ferranbt", "2021-05-07", "68", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["92", "0xpolygon/polygon-edge", "3", "2022-03-31", "munna0908", "2022-03-31", "69", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["99", "0xpolygon/polygon-sdk", "1", "2019-11-15", "ferranbt", "2019-11-15", "75", "1", "False", null, "1", "374.0", "0.1977217249796582", "243"], ["100", "0xpolygon/polygon-sdk", "2", "2021-05-07", "ferranbt", "2021-05-07", "76", "1", "False", "539.0", "1", "913.0", "0.2213181448331977", "272"], ["101", "0xpolygon/polygon-sdk", "3", "2022-03-31", "munna0908", "2022-03-31", "77", "1", "False", "328.0", "1", "230.0", "0.0349877949552481", "43"], ["108", "0xpolygonhermez/zkevm-node", "1", "2023-10-03", "Psykepro", "2023-10-03", "83", "1", "False", null, "1", "347.0", "0.0675580577058409", "96"], ["109", "0xpolygonmiden/crypto", "1", "2023-04-05", "vlopes11", "2023-04-05", "84", "1", "False", null, "1", "166.0", "0.0728476821192053", "22"], ["125", "0xproject/0x-starter-project", "1", "2018-08-28", "fabioberger", "2018-08-28", "98", "1", "False", null, "1", "306.0", "0.102803738317757", "11"], ["126", "0xproject/0x-starter-project", "2", "2019-12-06", "dekz", "2019-12-06", "99", "1", "False", "465.0", "1", "605.0", "0.4766355140186916", "51"], ["127", "0xs34n/starknet.js", "1", "2022-07-22", "<PERSON><PERSON><PERSON><PERSON>", "2022-07-22", "100", "1", "False", null, "1", "50.0", "0.0313930673642903", "48"], ["128", "0xs34n/starknet.js", "2", "2023-03-08", "irisdv", "2023-03-08", "101", "1", "False", "229.0", "1", "88.0", "0.0222367560497056", "34"], ["129", "0xs34n/starknet.js", "3", "2023-12-07", "janek26", "2023-12-07", "102", "1", "False", "274.0", "1", "778.0", "0.1366906474820144", "209"], ["130", "0xsky/xredis", "1", "2021-01-24", "0xsky", "2021-01-24", "103", "1", "False", null, "1", "2392.0", "0.6623376623376623", "153"], ["131", "0xsky/xredis", "2", "2022-03-22", "ox<PERSON>", "2022-03-22", "104", "1", "False", "422.0", "1", "1253.0", "0.2251082251082251", "52"], ["132", "0xsky/xredis", "3", "2023-06-13", "0xsky", "2023-06-13", "105", "1", "False", "448.0", "1", "3262.0", "0.6666666666666666", "154"], ["133", "0xspaceshard/starknet-hardhat-plugin", "1", "2022-03-17", "dribeiro-Shard<PERSON>abs", "2022-03-17", "106", "1", "False", null, "1", "104.0", "0.0857908847184986", "32"], ["134", "1-liners/1-liners", "1", "2015-07-05", "s<PERSON><PERSON><PERSON><PERSON>", "2015-07-05", "107", "1", "False", null, "1", "76.0", "0.2868852459016393", "105"], ["135", "101loop/drf-user", "1", "2019-04-02", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-04-02", "108", "1", "False", null, "1", "636.0", "0.40625", "52"]], "shape": {"columns": 13, "rows": 91248}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_id</th>\n", "      <th>attrition_time</th>\n", "      <th>dev_login</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>01mf02/jaq</td>\n", "      <td>1</td>\n", "      <td>2023-04-11</td>\n", "      <td>kammerchorinnsbruck</td>\n", "      <td>2023-04-11</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>854.0</td>\n", "      <td>0.155844</td>\n", "      <td>228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>1</td>\n", "      <td>2020-09-25</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2020-09-25</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>2189.0</td>\n", "      <td>0.548485</td>\n", "      <td>181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>05bit/peewee-async</td>\n", "      <td>2</td>\n", "      <td>2021-11-02</td>\n", "      <td>rud<PERSON><PERSON></td>\n", "      <td>2021-11-02</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>403.0</td>\n", "      <td>1</td>\n", "      <td>2592.0</td>\n", "      <td>0.551515</td>\n", "      <td>182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>1</td>\n", "      <td>2020-04-14</td>\n", "      <td>bothra90</td>\n", "      <td>2020-04-14</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>299.0</td>\n", "      <td>0.013271</td>\n", "      <td>93</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>0LNetworkCommunity/libra-legacy-v6</td>\n", "      <td>21</td>\n", "      <td>2021-12-21</td>\n", "      <td>gre<PERSON><PERSON><PERSON></td>\n", "      <td>2021-12-21</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>397.0</td>\n", "      <td>1</td>\n", "      <td>581.0</td>\n", "      <td>0.020405</td>\n", "      <td>143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174882</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>2</td>\n", "      <td>2018-08-13</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-08-13</td>\n", "      <td>157916</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>384.0</td>\n", "      <td>1</td>\n", "      <td>79.0</td>\n", "      <td>0.063158</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174883</th>\n", "      <td>zzzprojects/entityframework.dynamicfilters</td>\n", "      <td>3</td>\n", "      <td>2022-10-13</td>\n", "      <td><PERSON></td>\n", "      <td>2022-10-13</td>\n", "      <td>157917</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>1522.0</td>\n", "      <td>1</td>\n", "      <td>1806.0</td>\n", "      <td>0.210526</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174884</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>1</td>\n", "      <td>2017-07-31</td>\n", "      <td>waqasm78</td>\n", "      <td>2017-07-31</td>\n", "      <td>157918</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>27.0</td>\n", "      <td>0.016432</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174885</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>2</td>\n", "      <td>2018-11-09</td>\n", "      <td>stgelaisalex</td>\n", "      <td>2018-11-09</td>\n", "      <td>157919</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>466.0</td>\n", "      <td>1</td>\n", "      <td>87.0</td>\n", "      <td>0.023474</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174886</th>\n", "      <td>zzzprojects/html-agility-pack</td>\n", "      <td>3</td>\n", "      <td>2019-10-02</td>\n", "      <td>waqasm78</td>\n", "      <td>2019-10-02</td>\n", "      <td>157920</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>327.0</td>\n", "      <td>1</td>\n", "      <td>820.0</td>\n", "      <td>0.018779</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>91248 rows × 13 columns</p>\n", "</div>"], "text/plain": ["                                         repo_name  attrition_id  \\\n", "0                                       01mf02/jaq             1   \n", "1                               05bit/peewee-async             1   \n", "2                               05bit/peewee-async             2   \n", "3               0LNetworkCommunity/libra-legacy-v6             1   \n", "23              0LNetworkCommunity/libra-legacy-v6            21   \n", "...                                            ...           ...   \n", "174882  zzzprojects/entityframework.dynamicfilters             2   \n", "174883  zzzprojects/entityframework.dynamicfilters             3   \n", "174884               zzzprojects/html-agility-pack             1   \n", "174885               zzzprojects/html-agility-pack             2   \n", "174886               zzzprojects/html-agility-pack             3   \n", "\n", "       attrition_time            dev_login attrition_date   burst  \\\n", "0          2023-04-11  kammerchorinnsbruck     2023-04-11       1   \n", "1          2020-09-25              rudyryk     2020-09-25       2   \n", "2          2021-11-02              rudyryk     2021-11-02       3   \n", "3          2020-04-14             bothra90     2020-04-14       4   \n", "23         2021-12-21          gregnazario     2021-12-21      13   \n", "...               ...                  ...            ...     ...   \n", "174882     2018-08-13         stgelaisalex     2018-08-13  157916   \n", "174883     2022-10-13       <PERSON><PERSON>agnan     2022-10-13  157917   \n", "174884     2017-07-31             waqasm78     2017-07-31  157918   \n", "174885     2018-11-09         stgelaisalex     2018-11-09  157919   \n", "174886     2019-10-02             waqasm78     2019-10-02  157920   \n", "\n", "        attrition_count  gap_less_than_84  inter_burst_gap  someone_left  \\\n", "0                     1             False              NaN             1   \n", "1                     1             False              NaN             1   \n", "2                     1             False            403.0             1   \n", "3                     1             False              NaN             1   \n", "23                    1             False            397.0             1   \n", "...                 ...               ...              ...           ...   \n", "174882                1             False            384.0             1   \n", "174883                1             False           1522.0             1   \n", "174884                1             False              NaN             1   \n", "174885                1             False            466.0             1   \n", "174886                1             False            327.0             1   \n", "\n", "        tenure  commit_percent  commits  \n", "0        854.0        0.155844      228  \n", "1       2189.0        0.548485      181  \n", "2       2592.0        0.551515      182  \n", "3        299.0        0.013271       93  \n", "23       581.0        0.020405      143  \n", "...        ...             ...      ...  \n", "174882    79.0        0.063158       12  \n", "174883  1806.0        0.210526       40  \n", "174884    27.0        0.016432        7  \n", "174885    87.0        0.023474       10  \n", "174886   820.0        0.018779        8  \n", "\n", "[91248 rows x 13 columns]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["def merge_attrition_bursts(df):\n", "  \"\"\"\n", "  Merge records within the same burst if there are multiple attrition events.\n", "  For each group (same repo_name and burst):\n", "    - If there are multiple records:\n", "        - Set attrition_date as the latest (maximum) time.\n", "        - Compute tenure as the average.\n", "        - Sum commit_percent and commits.\n", "    - Otherwise, keep the record unchanged.\n", "  Returns a new DataFrame with the merged results.\n", "  \"\"\"\n", "  merged_records = []\n", "  grouped = df.groupby(['repo_name', 'burst'])\n", "  for (repo, burst), group in grouped:\n", "    if len(group) > 1:\n", "      merged = group.iloc[0].copy()  # copy common info\n", "      merged['attrition_date'] = group['attrition_date'].max()\n", "      merged['tenure'] = group['tenure'].mean()\n", "      merged['commit_percent'] = group['commit_percent'].sum()\n", "      merged['commits'] = group['commits'].sum()\n", "      merged_records.append(merged)\n", "    else:\n", "      merged_records.append(group.iloc[0])\n", "  return pd.DataFrame(merged_records)\n", "\n", "# Process attritions DataFrame and display the merged series data.\n", "attritions = attrition_with_burst_without_merge.copy()  # if needed\n", "attritions = attritions[attritions['tenure'].notnull()]\n", "attritions['tenure'] = attritions['tenure'].astype(int)\n", "attritions = merge_attrition_bursts(attritions)\n", "attritions"]}, {"cell_type": "code", "execution_count": 34, "id": "bd160079", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Are the two datasets identical (after renaming and sorting)? False\n", "Rows only in attritions (after merge/rename): 539\n", "                    repo_name attrition_developer attrition_date  burst  \\\n", "364  BuildOnViction/tomochain         nguyenbatam     2021-09-13    829   \n", "497             QuivrHQ/quivr             gozineb     2023-12-18   1170   \n", "515            Rust-GCC/gccrs       ArnaudCharlet     2001-12-11   1197   \n", "518            Rust-GCC/gccrs             amylaar     2006-12-18   1209   \n", "553    SouJunior/vagas-webapp    igords-goncalves     2023-07-28   1322   \n", "\n", "     attrition_count  gap_less_than_84  inter_burst_gap  someone_left  tenure  \\\n", "364                2             False            164.0             1  1207.0   \n", "497                2             False              NaN             1   202.0   \n", "515                2             False            661.0             1    33.5   \n", "518                2             False            112.0             1  3430.0   \n", "553                2             False            195.0             1   271.5   \n", "\n", "     commit_percent  commits     _merge  \n", "364        0.050370    443.0  left_only  \n", "497        0.153248    335.0  left_only  \n", "515        0.000160     34.0  left_only  \n", "518        0.001169    249.0  left_only  \n", "553        0.372716    306.0  left_only  \n", "Rows only in original: 539\n", "                    repo_name attrition_developer attrition_date  burst  \\\n", "363  BuildOnViction/tomochain         nguyenbatam     2021-09-13    829   \n", "496             QuivrHQ/quivr             gozineb     2023-12-18   1170   \n", "514            Rust-GCC/gccrs       ArnaudCharlet     2001-12-11   1197   \n", "517            Rust-GCC/gccrs             amylaar     2006-12-18   1209   \n", "552    SouJunior/vagas-webapp    igords-goncalves     2023-07-28   1322   \n", "\n", "     attrition_count  gap_less_than_84  inter_burst_gap  someone_left  tenure  \\\n", "363                2             False            164.0             1  1207.0   \n", "496                2             False              NaN             1   202.0   \n", "514                2             False            661.0             1    33.5   \n", "517                2             False            112.0             1  3430.0   \n", "552                2             False            195.0             1   271.5   \n", "\n", "     commit_percent  commits      _merge  \n", "363        0.050370    443.0  right_only  \n", "496        0.153248    335.0  right_only  \n", "514        0.000160     34.0  right_only  \n", "517        0.001169    249.0  right_only  \n", "552        0.372716    306.0  right_only  \n"]}], "source": ["# compare attrition_with_burst_original and attritions\n", "# Ensure both DataFrames have the same columns and comparable keys\n", "\n", "# Make a copy to avoid modifying original\n", "attritions_compare = attritions.copy()\n", "attritions_compare = attritions_compare.rename(columns={'dev_login': 'attrition_developer'})\n", "\n", "# Also ensure attrition_with_burst_original has the same column name for developer\n", "original_compare = attrition_with_burst_original.copy()\n", "original_compare = original_compare.rename(columns={'dev_login': 'attrition_developer'})\n", "\n", "# Ensure columns are in the same order\n", "common_cols = [col for col in attritions_compare.columns if col in original_compare.columns]\n", "attritions_compare = attritions_compare[common_cols]\n", "original_compare = original_compare[common_cols]\n", "\n", "# Sort both DataFrames for reliable comparison\n", "attritions_compare_sorted = attritions_compare.sort_values(by=common_cols).reset_index(drop=True)\n", "original_compare_sorted = original_compare.sort_values(by=common_cols).reset_index(drop=True)\n", "\n", "# Compare the DataFrames\n", "comparison = attritions_compare_sorted.equals(original_compare_sorted)\n", "print(f\"Are the two datasets identical (after renaming and sorting)? {comparison}\")\n", "\n", "# If not identical, show differences\n", "if not comparison:\n", "    # Find rows in attritions not in original\n", "    merged = attritions_compare_sorted.merge(\n", "        original_compare_sorted,\n", "        how='outer',\n", "        indicator=True\n", "    )\n", "    only_in_attritions = merged[merged['_merge'] == 'left_only']\n", "    only_in_original = merged[merged['_merge'] == 'right_only']\n", "    print(f\"Rows only in attritions (after merge/rename): {only_in_attritions.shape[0]}\")\n", "    if only_in_attritions.shape[0] > 0:\n", "        print(only_in_attritions.head())\n", "    print(f\"Rows only in original: {only_in_original.shape[0]}\")\n", "    if only_in_original.shape[0] > 0:\n", "        print(only_in_original.head())\n"]}, {"cell_type": "code", "execution_count": 35, "id": "e95fa580", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "_merge", "rawType": "category", "type": "unknown"}], "ref": "7744c343-3457-4103-92c6-ddb891436283", "rows": [["364", "BuildOnViction/tomochain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2021-09-13", "829", "2", "False", "164.0", "1", "1207.0", "0.050369528140989106", "443.0", "left_only"], ["497", "QuivrHQ/quivr", "gozineb", "2023-12-18", "1170", "2", "False", null, "1", "202.0", "0.15324794144556259", "335.0", "left_only"], ["515", "Rust-GCC/gccrs", "ArnaudCharlet", "2001-12-11", "1197", "2", "False", "661.0", "1", "33.5", "0.0001596049308534242", "34.0", "left_only"], ["518", "Rust-GCC/gccrs", "amylaar", "2006-12-18", "1209", "2", "False", "112.0", "1", "3430.0", "0.0011688714053682998", "249.0", "left_only"], ["553", "SouJunior/vagas-webapp", "igor<PERSON>-gonc<PERSON><PERSON>", "2023-07-28", "1322", "2", "False", "195.0", "1", "271.5", "0.37271619975639453", "306.0", "left_only"], ["579", "Xilinx/embeddedsw", "kinjalp27", "2017-03-25", "1593", "2", "False", "221.0", "1", "916.0", "0.018909518444128602", "386.0", "left_only"], ["658", "aave/protocol-v2", "Zer0dot", "2021-04-23", "1704", "3", "False", null, "1", "249.66666666666666", "0.525331254871395", "674.0", "left_only"], ["761", "absmach/magistrala", "mteodor", "2022-02-11", "1832", "2", "False", "101.0", "1", "1190.0", "0.07183725365543539", "113.0", "left_only"], ["770", "absmach/mainflux", "mteodor", "2022-02-11", "1840", "2", "False", "101.0", "1", "1190.0", "0.07089084065244661", "113.0", "left_only"], ["926", "acquia/blt", "grasmash", "2019-03-08", "2081", "2", "False", null, "1", "1029.5", "0.44076005961251863", "1183.0", "left_only"], ["995", "actions/toolkit", "dhadka", "2021-04-13", "2224", "2", "False", "120.0", "1", "405.0", "0.037468776019983205", "45.0", "left_only"], ["1150", "adam<PERSON>/learnxinyminutes-docs", "samcv", "2021-08-22", "2508", "2", "False", "88.0", "1", "1213.5", "0.0051713947990542995", "35.0", "left_only"], ["2068", "aiven/pghoard", "Ormod", "2019-01-16", "3867", "2", "False", "355.0", "1", "756.0", "0.15384615384615372", "132.0", "left_only"], ["2140", "akeneo/pim-community-dev", "willy-ahva", "2018-02-15", "3971", "1", "False", "119.0", "1", "583.0", "2.2760378732702117e-05", "1.0", "left_only"], ["2226", "akveo/react-native-ui-kitten", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2018-01-12", "4093", "2", "False", "193.0", "1", "202.5", "0.041463414634146295", "34.0", "left_only"], ["2375", "alephsecurity/xnu-qemu-arm64", "XanClic", "2011-09-19", "4285", "3", "False", "207.0", "1", "534.3333333333334", "0.0006094110477515312", "42.0", "left_only"], ["2379", "alephsecurity/xnu-qemu-arm64", "<PERSON><PERSON><PERSON><PERSON>", "2016-10-10", "4302", "2", "False", "94.0", "1", "1475.5", "0.0056878364456824995", "392.0", "left_only"], ["2474", "alexandrebarachant/muse-lsl", "jdpigeon", "2019-05-25", "4435", "2", "False", "355.0", "1", "774.0", "0.49101796407185616", "82.0", "left_only"], ["3016", "allure-framework/allure-js", "baev", "2019-08-28", "5460", "2", "False", "217.0", "1", "201.5", "0.009999999999999901", "6.0", "left_only"], ["3035", "alluxio/alluxio", "yupeng9", "2018-07-27", "5499", "2", "False", "157.0", "1", "874.5", "0.041019786910197795", "1078.0", "left_only"], ["3053", "alohae<PERSON>or/aloha-editor", "<PERSON><PERSON><PERSON>", "2018-02-28", "5562", "2", "False", "529.0", "1", "2485.5", "0.22831110524230813", "1729.0", "left_only"], ["3181", "alternc/alternc", "fufroma", "2014-06-26", "5706", "3", "False", "1103.0", "1", "1704.3333333333333", "0.32694063926940625", "1074.0", "left_only"], ["3296", "amazon-connect/amazon-connect-streams", "ctwomblyamzn", "2022-12-16", "5858", "2", "False", "191.0", "1", "1100.0", "0.15409309791332249", "96.0", "left_only"], ["3463", "ampproject/amphtml", "cathyxz", "2020-01-11", "6102", "2", "False", "150.0", "1", "750.5", "0.020307756725635798", "388.0", "left_only"], ["3569", "anandbaburajan/kukkee", "<PERSON><PERSON><PERSON><PERSON>", "2021-02-05", "6277", "3", "False", null, "1", "46.666666666666664", "0.2882758620689654", "209.0", "left_only"], ["3770", "angelozerr/angularjs-eclipse", "<PERSON><PERSON><PERSON><PERSON>", "2016-07-03", "6549", "2", "False", null, "1", "958.5", "0.9320388349514563", "384.0", "left_only"], ["3950", "angular/material", "<PERSON><PERSON><PERSON>urn", "2017-12-12", "6855", "2", "False", "96.0", "1", "1097.5", "0.056263914187411404", "278.0", "left_only"], ["4223", "ant-design/ant-design-mini", "wyj580231", "2023-07-21", "7521", "2", "False", "155.0", "1", "377.5", "0.28787878787878785", "228.0", "left_only"], ["4455", "apache/apisix-website", "1502<PERSON><PERSON><PERSON>-<PERSON>h", "2022-06-06", "7993", "2", "False", "227.0", "1", "468.5", "0.027397260273972497", "36.0", "left_only"], ["4466", "apache/arrow-rs", "matthe<PERSON><PERSON><PERSON>er", "2022-03-03", "8081", "2", "False", "89.0", "1", "150.5", "0.004927726675426999", "30.0", "left_only"], ["4765", "apache/incubator-doris", "EmmyMiao87", "2022-10-10", "8939", "2", "False", "288.0", "1", "709.0", "0.010348583877995643", "171.0", "left_only"], ["5120", "apidoc/apidoc", "rottmann", "2021-11-21", "9836", "2", "False", "1090.0", "1", "1588.0", "0.41523341523341517", "338.0", "left_only"], ["5231", "aporeto-inc/trireme-lib", "<PERSON><PERSON><PERSON><PERSON>", "2020-03-28", "10052", "2", "False", "135.0", "1", "1126.0", "0.12249208025343179", "116.0", "left_only"], ["5242", "app-vnext/polly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-10-21", "10067", "2", "False", "540.0", "1", "1153.5", "0.045220377790497895", "79.0", "left_only"], ["5798", "argoproj/argo-workflows", "changhc", "2023-04-21", "11056", "2", "False", "136.0", "1", "822.0", "0.010966212211025301", "37.0", "left_only"], ["5838", "aristanetworks/eossdk", "apech", "2015-05-19", "11101", "2", "False", null, "1", "397.0", "0.14896755162241881", "101.0", "left_only"], ["5950", "armmbed/mbedtls", "mprse", "2023-07-14", "11358", "2", "False", "295.0", "1", "1372.5", "0.045095867654184094", "1303.0", "left_only"], ["6181", "asb2m10/dexed", "baconpaul", "2022-06-11", "11683", "2", "False", "414.0", "1", "896.0", "0.15011547344110848", "65.0", "left_only"], ["6185", "ascend/pytorch", "wangqiang160", "2023-12-29", "11688", "2", "False", "116.0", "1", "587.0", "0.036780223093156296", "122.0", "left_only"], ["6298", "aspnet/aspnetwebstack", "rynowak", "2014-09-23", "11840", "2", "False", "131.0", "1", "523.5", "0.049929345266132796", "106.0", "left_only"], ["6304", "aspnet/azuresignalr-samples", "vicancy", "2020-03-16", "11848", "2", "False", "266.0", "1", "665.5", "0.12121212121212109", "32.0", "left_only"], ["6794", "audrey<PERSON>roy/cookiecutter-pypackage", "PsiACE", "2020-02-10", "12703", "2", "False", "727.0", "1", "848.0", "0.08221797323135739", "43.0", "left_only"], ["6896", "austintackaberry/ydkjs-exercises", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2018-09-18", "12834", "2", "False", "90.0", "1", "105.5", "0.18064516129032251", "84.0", "left_only"], ["6995", "auth0/rules", "Adam-Auth0", "2020-06-07", "13028", "2", "False", "174.0", "1", "0.5", "0.022911051212937898", "17.0", "left_only"], ["7209", "autoprotocol/autoprotocol-python", "yang<PERSON>o", "2016-07-26", "13398", "2", "False", "169.0", "1", "428.5", "0.11344537815126039", "108.0", "left_only"], ["7294", "aviabird/angularspree", "go<PERSON><PERSON><PERSON><PERSON>", "2019-01-16", "13527", "2", "False", "254.0", "1", "474.5", "0.27655986509274866", "164.0", "left_only"], ["7438", "aws-samples/aws-serverless-workshops", "mikedeck", "2019-06-24", "13820", "2", "False", "206.0", "1", "514.0", "0.21348314606741559", "95.0", "left_only"], ["7454", "aws-samples/serverless-patterns", "onlybakam", "2021-08-02", "13845", "2", "False", "85.0", "1", "40.5", "0.006877022653721599", "34.0", "left_only"], ["7675", "aws/sagemaker-tensorflow-training-toolkit", "na<PERSON><PERSON>", "2020-06-17", "14432", "2", "False", "266.0", "1", "762.5", "0.12350597609561739", "31.0", "left_only"], ["7989", "azkaban/azkaban-plugins", "rbpark", "2014-02-11", "14964", "2", "False", null, "1", "390.0", "0.11690647482014369", "65.0", "left_only"]], "shape": {"columns": 12, "rows": 539}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_developer</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>_merge</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>BuildOnViction/tomochain</td>\n", "      <td>ng<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2021-09-13</td>\n", "      <td>829</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>164.0</td>\n", "      <td>1</td>\n", "      <td>1207.0</td>\n", "      <td>0.050370</td>\n", "      <td>443.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>497</th>\n", "      <td>QuivrHQ/quivr</td>\n", "      <td>gozineb</td>\n", "      <td>2023-12-18</td>\n", "      <td>1170</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>202.0</td>\n", "      <td>0.153248</td>\n", "      <td>335.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>515</th>\n", "      <td>Rust-GCC/gccrs</td>\n", "      <td>ArnaudC<PERSON><PERSON></td>\n", "      <td>2001-12-11</td>\n", "      <td>1197</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>661.0</td>\n", "      <td>1</td>\n", "      <td>33.5</td>\n", "      <td>0.000160</td>\n", "      <td>34.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>518</th>\n", "      <td>Rust-GCC/gccrs</td>\n", "      <td>amyla<PERSON></td>\n", "      <td>2006-12-18</td>\n", "      <td>1209</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>112.0</td>\n", "      <td>1</td>\n", "      <td>3430.0</td>\n", "      <td>0.001169</td>\n", "      <td>249.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>553</th>\n", "      <td>SouJunior/vagas-webapp</td>\n", "      <td>igords-goncal<PERSON></td>\n", "      <td>2023-07-28</td>\n", "      <td>1322</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>195.0</td>\n", "      <td>1</td>\n", "      <td>271.5</td>\n", "      <td>0.372716</td>\n", "      <td>306.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91178</th>\n", "      <td>zendframework/zend-mvc</td>\n", "      <td>asgrim</td>\n", "      <td>2017-05-01</td>\n", "      <td>156911</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>129.0</td>\n", "      <td>1</td>\n", "      <td>1548.0</td>\n", "      <td>0.393018</td>\n", "      <td>698.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91269</th>\n", "      <td>zeromq/czmq</td>\n", "      <td>ji<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2019-02-04</td>\n", "      <td>157131</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>176.0</td>\n", "      <td>1</td>\n", "      <td>1155.0</td>\n", "      <td>0.049852</td>\n", "      <td>168.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91366</th>\n", "      <td>zettajs/zetta</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>2016-04-27</td>\n", "      <td>157335</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>732.5</td>\n", "      <td>0.452685</td>\n", "      <td>354.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91438</th>\n", "      <td>zigpy/zha-device-handlers</td>\n", "      <td>MattWestb</td>\n", "      <td>2023-09-29</td>\n", "      <td>157432</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>198.0</td>\n", "      <td>1</td>\n", "      <td>1287.5</td>\n", "      <td>0.094408</td>\n", "      <td>130.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91716</th>\n", "      <td>zulip/python-zulip-api</td>\n", "      <td>aero31aero</td>\n", "      <td>2020-05-02</td>\n", "      <td>157779</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>407.0</td>\n", "      <td>1</td>\n", "      <td>1130.5</td>\n", "      <td>0.091170</td>\n", "      <td>222.0</td>\n", "      <td>left_only</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>539 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                       repo_name attrition_developer attrition_date   burst  \\\n", "364     BuildOnViction/tomochain         nguyenbatam     2021-09-13     829   \n", "497                QuivrHQ/quivr             gozineb     2023-12-18    1170   \n", "515               Rust-GCC/gccrs       ArnaudCharlet     2001-12-11    1197   \n", "518               Rust-GCC/gccrs             amylaar     2006-12-18    1209   \n", "553       SouJunior/vagas-webapp    igords-goncalves     2023-07-28    1322   \n", "...                          ...                 ...            ...     ...   \n", "91178     zendframework/zend-mvc              asgrim     2017-05-01  156911   \n", "91269                zeromq/czmq           jim<PERSON><PERSON>ov     2019-02-04  157131   \n", "91366              zettajs/zetta             mdobson     2016-04-27  157335   \n", "91438  zigpy/zha-device-handlers           MattWestb     2023-09-29  157432   \n", "91716     zulip/python-zulip-api          aero31aero     2020-05-02  157779   \n", "\n", "       attrition_count  gap_less_than_84  inter_burst_gap  someone_left  \\\n", "364                  2             False            164.0             1   \n", "497                  2             False              NaN             1   \n", "515                  2             False            661.0             1   \n", "518                  2             False            112.0             1   \n", "553                  2             False            195.0             1   \n", "...                ...               ...              ...           ...   \n", "91178                3             False            129.0             1   \n", "91269                2             False            176.0             1   \n", "91366                2             False              NaN             1   \n", "91438                2             False            198.0             1   \n", "91716                2             False            407.0             1   \n", "\n", "       tenure  commit_percent  commits     _merge  \n", "364    1207.0        0.050370    443.0  left_only  \n", "497     202.0        0.153248    335.0  left_only  \n", "515      33.5        0.000160     34.0  left_only  \n", "518    3430.0        0.001169    249.0  left_only  \n", "553     271.5        0.372716    306.0  left_only  \n", "...       ...             ...      ...        ...  \n", "91178  1548.0        0.393018    698.0  left_only  \n", "91269  1155.0        0.049852    168.0  left_only  \n", "91366   732.5        0.452685    354.0  left_only  \n", "91438  1287.5        0.094408    130.0  left_only  \n", "91716  1130.5        0.091170    222.0  left_only  \n", "\n", "[539 rows x 12 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["only_in_attritions"]}, {"cell_type": "code", "execution_count": 36, "id": "e9bb8855", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "repo_name", "rawType": "object", "type": "string"}, {"name": "attrition_developer", "rawType": "object", "type": "string"}, {"name": "attrition_date", "rawType": "object", "type": "string"}, {"name": "burst", "rawType": "int64", "type": "integer"}, {"name": "attrition_count", "rawType": "int64", "type": "integer"}, {"name": "gap_less_than_84", "rawType": "bool", "type": "boolean"}, {"name": "inter_burst_gap", "rawType": "float64", "type": "float"}, {"name": "someone_left", "rawType": "int64", "type": "integer"}, {"name": "tenure", "rawType": "float64", "type": "float"}, {"name": "commit_percent", "rawType": "float64", "type": "float"}, {"name": "commits", "rawType": "float64", "type": "float"}, {"name": "_merge", "rawType": "category", "type": "unknown"}], "ref": "6c3934c3-f216-4b58-9667-af21e203071c", "rows": [["363", "BuildOnViction/tomochain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2021-09-13", "829", "2", "False", "164.0", "1", "1207.0", "0.0503695281409891", "443.0", "right_only"], ["496", "QuivrHQ/quivr", "gozineb", "2023-12-18", "1170", "2", "False", null, "1", "202.0", "0.1532479414455625", "335.0", "right_only"], ["514", "Rust-GCC/gccrs", "ArnaudCharlet", "2001-12-11", "1197", "2", "False", "661.0", "1", "33.5", "0.0001596049308534", "34.0", "right_only"], ["517", "Rust-GCC/gccrs", "amylaar", "2006-12-18", "1209", "2", "False", "112.0", "1", "3430.0", "0.0011688714053682", "249.0", "right_only"], ["552", "SouJunior/vagas-webapp", "igor<PERSON>-gonc<PERSON><PERSON>", "2023-07-28", "1322", "2", "False", "195.0", "1", "271.5", "0.3727161997563945", "306.0", "right_only"], ["578", "Xilinx/embeddedsw", "kinjalp27", "2017-03-25", "1593", "2", "False", "221.0", "1", "916.0", "0.0189095184441286", "386.0", "right_only"], ["657", "aave/protocol-v2", "Zer0dot", "2021-04-23", "1704", "3", "False", null, "1", "249.66666666666663", "0.525331254871395", "674.0", "right_only"], ["760", "absmach/magistrala", "mteodor", "2022-02-11", "1832", "2", "False", "101.0", "1", "1190.0", "0.0718372536554353", "113.0", "right_only"], ["769", "absmach/mainflux", "mteodor", "2022-02-11", "1840", "2", "False", "101.0", "1", "1190.0", "0.0708908406524466", "113.0", "right_only"], ["925", "acquia/blt", "grasmash", "2019-03-08", "2081", "2", "False", null, "1", "1029.5", "0.4407600596125186", "1183.0", "right_only"], ["994", "actions/toolkit", "dhadka", "2021-04-13", "2224", "2", "False", "120.0", "1", "405.0", "0.0374687760199832", "45.0", "right_only"], ["1149", "adam<PERSON>/learnxinyminutes-docs", "samcv", "2021-08-22", "2508", "2", "False", "88.0", "1", "1213.5", "0.0051713947990542", "35.0", "right_only"], ["2067", "aiven/pghoard", "Ormod", "2019-01-16", "3867", "2", "False", "355.0", "1", "756.0", "0.1538461538461537", "132.0", "right_only"], ["2141", "akeneo/pim-community-dev", "willy-ahva", "2018-02-15", "3971", "1", "False", "119.0", "1", "583.0", "2.276037873270212e-05", "1.0", "right_only"], ["2225", "akveo/react-native-ui-kitten", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2018-01-12", "4093", "2", "False", "193.0", "1", "202.5", "0.0414634146341462", "34.0", "right_only"], ["2374", "alephsecurity/xnu-qemu-arm64", "XanClic", "2011-09-19", "4285", "3", "False", "207.0", "1", "534.3333333333334", "0.0006094110477515", "42.0", "right_only"], ["2378", "alephsecurity/xnu-qemu-arm64", "<PERSON><PERSON><PERSON><PERSON>", "2016-10-10", "4302", "2", "False", "94.0", "1", "1475.5", "0.0056878364456824", "392.0", "right_only"], ["2473", "alexandrebarachant/muse-lsl", "jdpigeon", "2019-05-25", "4435", "2", "False", "355.0", "1", "774.0", "0.4910179640718561", "82.0", "right_only"], ["3015", "allure-framework/allure-js", "baev", "2019-08-28", "5460", "2", "False", "217.0", "1", "201.5", "0.0099999999999999", "6.0", "right_only"], ["3034", "alluxio/alluxio", "yupeng9", "2018-07-27", "5499", "2", "False", "157.0", "1", "874.5", "0.0410197869101977", "1078.0", "right_only"], ["3052", "alohae<PERSON>or/aloha-editor", "<PERSON><PERSON><PERSON>", "2018-02-28", "5562", "2", "False", "529.0", "1", "2485.5", "0.2283111052423081", "1729.0", "right_only"], ["3180", "alternc/alternc", "fufroma", "2014-06-26", "5706", "3", "False", "1103.0", "1", "1704.3333333333333", "0.3269406392694062", "1074.0", "right_only"], ["3295", "amazon-connect/amazon-connect-streams", "ctwomblyamzn", "2022-12-16", "5858", "2", "False", "191.0", "1", "1100.0", "0.1540930979133224", "96.0", "right_only"], ["3462", "ampproject/amphtml", "cathyxz", "2020-01-11", "6102", "2", "False", "150.0", "1", "750.5", "0.0203077567256357", "388.0", "right_only"], ["3568", "anandbaburajan/kukkee", "<PERSON><PERSON><PERSON><PERSON>", "2021-02-05", "6277", "3", "False", null, "1", "46.66666666666666", "0.2882758620689654", "209.0", "right_only"], ["3771", "angelozerr/angularjs-eclipse", "<PERSON><PERSON><PERSON><PERSON>", "2016-07-03", "6549", "2", "False", null, "1", "958.5", "0.9320388349514565", "384.0", "right_only"], ["3949", "angular/material", "<PERSON><PERSON><PERSON>urn", "2017-12-12", "6855", "2", "False", "96.0", "1", "1097.5", "0.0562639141874114", "278.0", "right_only"], ["4222", "ant-design/ant-design-mini", "wyj580231", "2023-07-21", "7521", "2", "False", "155.0", "1", "377.5", "0.2878787878787878", "228.0", "right_only"], ["4454", "apache/apisix-website", "1502<PERSON><PERSON><PERSON>-<PERSON>h", "2022-06-06", "7993", "2", "False", "227.0", "1", "468.5", "0.0273972602739724", "36.0", "right_only"], ["4465", "apache/arrow-rs", "matthe<PERSON><PERSON><PERSON>er", "2022-03-03", "8081", "2", "False", "89.0", "1", "150.5", "0.0049277266754269", "30.0", "right_only"], ["4764", "apache/incubator-doris", "EmmyMiao87", "2022-10-10", "8939", "2", "False", "288.0", "1", "709.0", "0.0103485838779956", "171.0", "right_only"], ["5119", "apidoc/apidoc", "rottmann", "2021-11-21", "9836", "2", "False", "1090.0", "1", "1588.0", "0.4152334152334151", "338.0", "right_only"], ["5230", "aporeto-inc/trireme-lib", "<PERSON><PERSON><PERSON><PERSON>", "2020-03-28", "10052", "2", "False", "135.0", "1", "1126.0", "0.1224920802534317", "116.0", "right_only"], ["5241", "app-vnext/polly", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2019-10-21", "10067", "2", "False", "540.0", "1", "1153.5", "0.0452203777904978", "79.0", "right_only"], ["5797", "argoproj/argo-workflows", "changhc", "2023-04-21", "11056", "2", "False", "136.0", "1", "822.0", "0.0109662122110253", "37.0", "right_only"], ["5837", "aristanetworks/eossdk", "apech", "2015-05-19", "11101", "2", "False", null, "1", "397.0", "0.1489675516224188", "101.0", "right_only"], ["5949", "armmbed/mbedtls", "mprse", "2023-07-14", "11358", "2", "False", "295.0", "1", "1372.5", "0.045095867654184", "1303.0", "right_only"], ["6180", "asb2m10/dexed", "baconpaul", "2022-06-11", "11683", "2", "False", "414.0", "1", "896.0", "0.1501154734411084", "65.0", "right_only"], ["6184", "ascend/pytorch", "wangqiang160", "2023-12-29", "11688", "2", "False", "116.0", "1", "587.0", "0.0367802230931562", "122.0", "right_only"], ["6297", "aspnet/aspnetwebstack", "rynowak", "2014-09-23", "11840", "2", "False", "131.0", "1", "523.5", "0.0499293452661327", "106.0", "right_only"], ["6303", "aspnet/azuresignalr-samples", "vicancy", "2020-03-16", "11848", "2", "False", "266.0", "1", "665.5", "0.121212121212121", "32.0", "right_only"], ["6793", "audrey<PERSON>roy/cookiecutter-pypackage", "PsiACE", "2020-02-10", "12703", "2", "False", "727.0", "1", "848.0", "0.0822179732313573", "43.0", "right_only"], ["6895", "austintackaberry/ydkjs-exercises", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "2018-09-18", "12834", "2", "False", "90.0", "1", "105.5", "0.1806451612903225", "84.0", "right_only"], ["6994", "auth0/rules", "Adam-Auth0", "2020-06-07", "13028", "2", "False", "174.0", "1", "0.5", "0.0229110512129378", "17.0", "right_only"], ["7208", "autoprotocol/autoprotocol-python", "yang<PERSON>o", "2016-07-26", "13398", "2", "False", "169.0", "1", "428.5", "0.1134453781512603", "108.0", "right_only"], ["7293", "aviabird/angularspree", "go<PERSON><PERSON><PERSON><PERSON>", "2019-01-16", "13527", "2", "False", "254.0", "1", "474.5", "0.2765598650927486", "164.0", "right_only"], ["7437", "aws-samples/aws-serverless-workshops", "mikedeck", "2019-06-24", "13820", "2", "False", "206.0", "1", "514.0", "0.2134831460674155", "95.0", "right_only"], ["7453", "aws-samples/serverless-patterns", "onlybakam", "2021-08-02", "13845", "2", "False", "85.0", "1", "40.5", "0.0068770226537215", "34.0", "right_only"], ["7674", "aws/sagemaker-tensorflow-training-toolkit", "na<PERSON><PERSON>", "2020-06-17", "14432", "2", "False", "266.0", "1", "762.5", "0.1235059760956173", "31.0", "right_only"], ["7988", "azkaban/azkaban-plugins", "rbpark", "2014-02-11", "14964", "2", "False", null, "1", "390.0", "0.1169064748201436", "65.0", "right_only"]], "shape": {"columns": 12, "rows": 539}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>repo_name</th>\n", "      <th>attrition_developer</th>\n", "      <th>attrition_date</th>\n", "      <th>burst</th>\n", "      <th>attrition_count</th>\n", "      <th>gap_less_than_84</th>\n", "      <th>inter_burst_gap</th>\n", "      <th>someone_left</th>\n", "      <th>tenure</th>\n", "      <th>commit_percent</th>\n", "      <th>commits</th>\n", "      <th>_merge</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>BuildOnViction/tomochain</td>\n", "      <td>ng<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2021-09-13</td>\n", "      <td>829</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>164.0</td>\n", "      <td>1</td>\n", "      <td>1207.0</td>\n", "      <td>0.050370</td>\n", "      <td>443.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>496</th>\n", "      <td>QuivrHQ/quivr</td>\n", "      <td>gozineb</td>\n", "      <td>2023-12-18</td>\n", "      <td>1170</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>202.0</td>\n", "      <td>0.153248</td>\n", "      <td>335.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>514</th>\n", "      <td>Rust-GCC/gccrs</td>\n", "      <td>ArnaudC<PERSON><PERSON></td>\n", "      <td>2001-12-11</td>\n", "      <td>1197</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>661.0</td>\n", "      <td>1</td>\n", "      <td>33.5</td>\n", "      <td>0.000160</td>\n", "      <td>34.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>517</th>\n", "      <td>Rust-GCC/gccrs</td>\n", "      <td>amyla<PERSON></td>\n", "      <td>2006-12-18</td>\n", "      <td>1209</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>112.0</td>\n", "      <td>1</td>\n", "      <td>3430.0</td>\n", "      <td>0.001169</td>\n", "      <td>249.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>552</th>\n", "      <td>SouJunior/vagas-webapp</td>\n", "      <td>igords-goncal<PERSON></td>\n", "      <td>2023-07-28</td>\n", "      <td>1322</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>195.0</td>\n", "      <td>1</td>\n", "      <td>271.5</td>\n", "      <td>0.372716</td>\n", "      <td>306.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91177</th>\n", "      <td>zendframework/zend-mvc</td>\n", "      <td>asgrim</td>\n", "      <td>2017-05-01</td>\n", "      <td>156911</td>\n", "      <td>3</td>\n", "      <td>False</td>\n", "      <td>129.0</td>\n", "      <td>1</td>\n", "      <td>1548.0</td>\n", "      <td>0.393018</td>\n", "      <td>698.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91268</th>\n", "      <td>zeromq/czmq</td>\n", "      <td>ji<PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2019-02-04</td>\n", "      <td>157131</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>176.0</td>\n", "      <td>1</td>\n", "      <td>1155.0</td>\n", "      <td>0.049852</td>\n", "      <td>168.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91365</th>\n", "      <td>zettajs/zetta</td>\n", "      <td>m<PERSON><PERSON></td>\n", "      <td>2016-04-27</td>\n", "      <td>157335</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>732.5</td>\n", "      <td>0.452685</td>\n", "      <td>354.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91437</th>\n", "      <td>zigpy/zha-device-handlers</td>\n", "      <td>MattWestb</td>\n", "      <td>2023-09-29</td>\n", "      <td>157432</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>198.0</td>\n", "      <td>1</td>\n", "      <td>1287.5</td>\n", "      <td>0.094408</td>\n", "      <td>130.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91715</th>\n", "      <td>zulip/python-zulip-api</td>\n", "      <td>aero31aero</td>\n", "      <td>2020-05-02</td>\n", "      <td>157779</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>407.0</td>\n", "      <td>1</td>\n", "      <td>1130.5</td>\n", "      <td>0.091170</td>\n", "      <td>222.0</td>\n", "      <td>right_only</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>539 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                       repo_name attrition_developer attrition_date   burst  \\\n", "363     BuildOnViction/tomochain         nguyenbatam     2021-09-13     829   \n", "496                QuivrHQ/quivr             gozineb     2023-12-18    1170   \n", "514               Rust-GCC/gccrs       ArnaudCharlet     2001-12-11    1197   \n", "517               Rust-GCC/gccrs             amylaar     2006-12-18    1209   \n", "552       SouJunior/vagas-webapp    igords-goncalves     2023-07-28    1322   \n", "...                          ...                 ...            ...     ...   \n", "91177     zendframework/zend-mvc              asgrim     2017-05-01  156911   \n", "91268                zeromq/czmq           jim<PERSON><PERSON>ov     2019-02-04  157131   \n", "91365              zettajs/zetta             mdobson     2016-04-27  157335   \n", "91437  zigpy/zha-device-handlers           MattWestb     2023-09-29  157432   \n", "91715     zulip/python-zulip-api          aero31aero     2020-05-02  157779   \n", "\n", "       attrition_count  gap_less_than_84  inter_burst_gap  someone_left  \\\n", "363                  2             False            164.0             1   \n", "496                  2             False              NaN             1   \n", "514                  2             False            661.0             1   \n", "517                  2             False            112.0             1   \n", "552                  2             False            195.0             1   \n", "...                ...               ...              ...           ...   \n", "91177                3             False            129.0             1   \n", "91268                2             False            176.0             1   \n", "91365                2             False              NaN             1   \n", "91437                2             False            198.0             1   \n", "91715                2             False            407.0             1   \n", "\n", "       tenure  commit_percent  commits      _merge  \n", "363    1207.0        0.050370    443.0  right_only  \n", "496     202.0        0.153248    335.0  right_only  \n", "514      33.5        0.000160     34.0  right_only  \n", "517    3430.0        0.001169    249.0  right_only  \n", "552     271.5        0.372716    306.0  right_only  \n", "...       ...             ...      ...         ...  \n", "91177  1548.0        0.393018    698.0  right_only  \n", "91268  1155.0        0.049852    168.0  right_only  \n", "91365   732.5        0.452685    354.0  right_only  \n", "91437  1287.5        0.094408    130.0  right_only  \n", "91715  1130.5        0.091170    222.0  right_only  \n", "\n", "[539 rows x 12 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["only_in_original"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}