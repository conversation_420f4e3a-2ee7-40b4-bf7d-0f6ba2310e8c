import pandas as pd
import numpy as np
import logging
from sklearn.neighbors import NearestNeighbors
import bisect
import pickle
import time
import gc
from datetime import datetime
from typing import Dict, List, Tuple, Generator

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def process_in_batches(
    data: pd.DataFrame,
    batch_size: int = 1000
) -> Generator[pd.DataFrame, None, None]:
    """按批次处理数据，避免一次性加载全部数据"""
    for i in range(0, len(data), batch_size):
        yield data.iloc[i:i + batch_size]

def compile_control_group_psm_knn(
    treatment_repos_with_left_date: pd.DataFrame,
    candidate_repos: List[str],
    productivity_metric_data: pd.DataFrame,
    n_neighbors: int,
    timewindow_weeks: int,
    feature_columns: List[str],
    extra_candidates: int = 10,
    batch_size: int = 1000,  # 添加批处理大小参数
) -> <PERSON>ple[Dict, pd.DataFrame, pd.DataFrame]:
    """
    优化后的PSM-KNN匹配函数，使用批处理和内存管理
    """
    logging.info("Starting precision PSM with NearestNeighbors...")

    # 预处理控制组数据
    treatment_bursts = set(treatment_repos_with_left_date["burst"])
    treatment_repos = set(treatment_repos_with_left_date["repo_name"])
    available_controls = set(candidate_repos)

    logging.info(f"Available controls: {len(available_controls)} repositories to match {len(treatment_repos)} treatment repositories in {len(treatment_bursts)} bursts")

    # 构建带时间标识的控制组数据
    control_data = productivity_metric_data[
        productivity_metric_data["repo_name"].isin(available_controls)
    ].copy()

    # 使用更高效的方式创建time_key
    control_data["time_key"] = list(zip(
        control_data["repo_name"],
        control_data["standardized_time_weeks"]
    ))

    # 预计算控制组离职时间点
    control_treatment_weeks = {}
    for name, group in productivity_metric_data[
        (productivity_metric_data["someone_left"] == 1) &
        (productivity_metric_data["repo_name"].isin(available_controls))
    ].groupby("repo_name")["standardized_time_weeks"]:
        control_treatment_weeks[name] = sorted(group.values)

    # 准备特征矩阵
    treatment_mask = (
        productivity_metric_data["repo_name"].isin(treatment_repos)
    ) & (productivity_metric_data["someone_left"] == 1)
    treatment_features_df = productivity_metric_data[treatment_mask].copy()

    X_control = control_data[feature_columns].values

    # 构建NearestNeighbors索引
    nn_model = NearestNeighbors(
        n_neighbors=min(len(X_control), n_neighbors * extra_candidates),
        algorithm='auto'
    )
    nn_model.fit(X_control)

    matched_pairs = {}
    control_data_indexed = control_data.reset_index(drop=True)

    # 批量处理treatment cases
    for batch_df in process_in_batches(treatment_features_df, batch_size):
        for _, t_row in batch_df.iterrows():
            t_burst = t_row["burst"]
            t_repo = t_row["repo_name"]
            t_time = t_row["standardized_time_weeks"]
            used_time_keys = set()
            matched_controls = []

            neighbor_query_size = n_neighbors * extra_candidates
            t_features = t_row[feature_columns].values.reshape(1, -1)

            while len(matched_controls) < n_neighbors and neighbor_query_size <= len(X_control):
                distances, indices = nn_model.kneighbors(
                    t_features,
                    n_neighbors=neighbor_query_size,
                    return_distance=True
                )
                indices = indices[0]
                distances = distances[0]

                # 使用列表推导式优化
                candidate_indices = [
                    idx for idx in indices
                    if control_data_indexed.iloc[idx]["time_key"] not in used_time_keys
                ]

                for idx in candidate_indices:
                    if len(matched_controls) >= n_neighbors:
                        break

                    control_row = control_data_indexed.iloc[idx]
                    control_repo, control_time = control_row["time_key"]

                    # 时间窗口过滤
                    if not (t_time - 4 <= control_time <= t_time + 4):
                        continue

                    if control_repo in [c["repo_name"] for c in matched_controls]:
                        continue

                    # 验证控制组的离职事件
                    treatment_weeks = control_treatment_weeks.get(control_repo, [])
                    if treatment_weeks:
                        window_start = control_time - timewindow_weeks
                        window_end = control_time + timewindow_weeks

                        left = bisect.bisect_left(treatment_weeks, window_start)
                        right = bisect.bisect_right(treatment_weeks, window_end)
                        if left < right:
                            continue

                    matched_controls.append({
                        "repo_name": control_repo,
                        "matched_time": control_time,
                        "features": control_row[feature_columns].values,
                    })
                    used_time_keys.add(control_row["time_key"])

                neighbor_query_size *= 2

            if matched_controls:
                matched_pairs[t_burst] = {
                    "burst": t_burst,
                    "repo_name": t_repo,
                    "treatment_time": t_time,
                    "controls": matched_controls,
                    "treatment_features": t_row[feature_columns].values,
                    "control_features": np.array([c["features"] for c in matched_controls]),
                }
                logging.info(f"Matched burst:{t_burst}-repo:{t_repo} with {len(matched_controls)} controls")
            else:
                logging.warning(f"No valid controls for {t_repo}")

        # 批次处理完成后进行垃圾回收
        gc.collect()

    logging.info(f"Matching completed. Total matched pairs: {len(matched_pairs)}")

    # 清理中间变量
    del control_treatment_weeks
    gc.collect()

    return matched_pairs, treatment_features_df, control_data_indexed

def test_improved_psm():
    """测试改进版PSM算法"""
    print("="*60)
    print("TESTING IMPROVED PSM ALGORITHM")
    print("="*60)
    
    start_time = time.time()
    
    # 读取测试数据
    test_file = 'result/20250730_did_result/productivity_with_propensity_scores_with_attritions_365_test_sample.csv'
    print(f"Loading test data from: {test_file}")
    p_test = pd.read_csv(test_file)
    print(f"Test data shape: {p_test.shape}")
    
    # 数据预处理 - 完全按照原始逻辑
    p_test_attrition = p_test[p_test['someone_left'] == 1].copy()
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'].notnull()]
    p_test_attrition = p_test_attrition[p_test_attrition['feature_sigmod_add'] != 0.5]  # 使用修改后的条件
    
    print(f"Attrition data shape after filtering: {p_test_attrition.shape}")
    
    p_test = p_test.fillna(0)
    p_test_attrition = p_test_attrition.fillna(0)
    
    # 转换类型
    if 'burst' in p_test_attrition.columns:
        p_test_attrition['burst'] = p_test_attrition['burst'].astype(int)
    
    # 调用 PSM 函数进行匹配
    print("Starting PSM matching...")
    matched_pairs, treatment_features_df, control_features_df = compile_control_group_psm_knn(
        p_test_attrition,
        p_test['repo_name'].tolist(),
        p_test,
        n_neighbors=5,
        timewindow_weeks=12,
        feature_columns=['feature_sigmod_add'],
        extra_candidates=10,
        batch_size=1000
    )
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n=== IMPROVED PSM RESULTS ===")
    print(f"Processing time: {processing_time:.2f} seconds")
    print(f"Total matched pairs: {len(matched_pairs)}")
    print(f"Treatment features shape: {treatment_features_df.shape}")
    print(f"Control features shape: {control_features_df.shape}")
    
    # 保存结果用于对比
    output_file = 'test_results_improved.pkl'
    with open(output_file, 'wb') as f:
        pickle.dump({
            'matched_pairs': matched_pairs,
            'treatment_features_df': treatment_features_df,
            'control_features_df': control_features_df,
            'processing_time': processing_time
        }, f)
    print(f"Results saved to: {output_file}")
    
    # 显示一些详细信息
    if matched_pairs:
        first_key = list(matched_pairs.keys())[0]
        first_match = matched_pairs[first_key]
        print(f"\nSample match (burst {first_key}):")
        print(f"  Repo: {first_match['repo_name']}")
        print(f"  Treatment time: {first_match['treatment_time']}")
        print(f"  Number of controls: {len(first_match['controls'])}")
        print(f"  Control repos: {[c['repo_name'] for c in first_match['controls']]}")
    
    return matched_pairs, processing_time

if __name__ == "__main__":
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    test_improved_psm()
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
